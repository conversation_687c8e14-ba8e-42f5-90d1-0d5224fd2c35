<template>
    <!-- 用户-用户列表 -->
    <div>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt"
              :padding="0">
            <div class="padding-add">
                <!-- 筛选条件 -->
                <Form ref="userFrom"
                      :model="userFrom"
                      :label-width="labelWidth"
                      :label-position="labelPosition"
                      @submit.native.prevent>
                    <Row :gutter="24">
                        <!-- <Col span="18">
                        <Row>
                            <Col>
                            <FormItem label="用户搜索："
                                      label-for="patient_name">
                                <Input v-model="userFrom.patient_name"
                                       placeholder="请输入"
                                       element-id="patient_name"
                                       clearable
                                       class="input-add">
                                <Select v-model="field_key"
                                        slot="prepend"
                                        style="width: 80px">
                                    <Option value="all">全部</Option>
                                    <Option value="card_no">就诊人ID</Option>
                                    <Option value="id_card">身份证号</Option>
                                    <Option value="phone_number">手机号</Option>
                                    <Option value="patient_name">就诊人</Option>
                                </Select>
                                </Input>
                            </FormItem>
                            </Col>

                        </Row>
                        </Col> -->
                        <template >
                            <Col span="18">
                            <Row>
                               <Col>
                                <!-- 新增就诊人ID输入框 -->
                                    <FormItem label="就诊人ID：" label-for="card_no">
                                        <Input v-model="userFrom.card_no" placeholder="请输入就诊人ID" class="input-add" clearable></Input>
                                    </FormItem>
                                </Col>
                                <Col>
                        <!-- 新增身份证号输入框 -->
                                    <FormItem label="身份证号：" label-for="id_card">
                                        <Input v-model="userFrom.id_card" placeholder="请输入身份证号" class="input-add" clearable></Input>
                                    </FormItem>
                                </Col>
                                <Col>
                        <!-- 新增手机号输入框 -->
                                    <FormItem label="手机号:" label-for="phone_number">
                                        <Input v-model="userFrom.phone_number" placeholder="请输入手机号" class="input-add" clearable></Input>
                                    </FormItem>
                                </Col>
                                <Col>
                        <!-- 新增就诊人输入框 -->
                                    <FormItem label="就诊人:" label-for="patient_name">
                                        <Input v-model="userFrom.patient_name" placeholder="请输入就诊人" class="input-add" clearable></Input>
                                    </FormItem>
                                </Col>
                            </Row>
                            </Col>
                        </template>
                        <Col span="6"
                             class="ivu-text-right userFrom">
                        <FormItem>
                            <Button type="primary"
                                    label="default"
                                    class="mr15"
                                    @click="userSearchs">搜索</Button>
                            <Button class="ResetSearch"
                                    @click="reset('userFrom')">重置</Button>
                            <!-- <a v-font="14"
                               class="ivu-ml-8"
                               @click="collapse = !collapse">
                                <template v-if="!collapse">
                                    展开
                                    <Icon type="ios-arrow-down" />
                                </template>
                                <template v-else>
                                    收起
                                    <Icon type="ios-arrow-up" />
                                </template>
                            </a> -->
                        </FormItem>
                        </Col>
                    </Row>
                </Form>
            </div>
        </Card>
        <Card :bordered="false"
              dis-hover
              class="ivu-mt listbox">
            <div class="new_tab">
                <!-- Tab栏切换 -->
                <Tabs v-model="headeType"
                      @on-click="onClickTab">
                    <TabPane :label="item.name"
                             :name="item.type"
                             v-for="(item, index) in headeNum"
                             :key="index" />
                </Tabs>
            </div>

            <!-- 用户列表表格 -->
            <vxe-table ref="xTable"
                       class="mt25"
                       :loading="loading"
                       row-id="uid"
                       :expand-config="{accordion: true}"
                       :checkbox-config="{reserve: true}"
                       @checkbox-all="checkboxAll"
                       @checkbox-change="checkboxItem"
                       :data="userLists">
                <!-- <vxe-column type="checkbox"
                            width="100">
                    <template #header>
                        <div>
                            <Dropdown transfer
                                      @on-click="allPages">
                                <a href="javascript:void(0)"
                                   class="acea-row row-middle">
                                    <span>全选({{isAll==1?(total-checkUidList.length):checkUidList.length}})</span>
                                    <Icon type="ios-arrow-down"></Icon>
                                </a>
                                <template #list>
                                    <DropdownMenu>
                                        <DropdownItem name="0">当前页</DropdownItem>
                                        <DropdownItem name="1">所有页</DropdownItem>
                                    </DropdownMenu>
                                </template>
                            </Dropdown>
                        </div>
                    </template>
                </vxe-column> -->
                <vxe-column field="card_no"
                            title="就诊人ID"
                            width="200"></vxe-column>
                <vxe-column field="patient_name"
                            title="就诊人"
                            min-width="80">
                </vxe-column>
                <vxe-column field="id_card"
                            title="身份证号"
                            min-width="110"></vxe-column>
                <vxe-column field="phone_number"
                            title="手机号"
                            min-width="110"> </vxe-column>
                <vxe-column field="nickname"
                            title="绑定用户"
                            min-width="80"><template v-slot="{ row }">
                        <a @click="changeMenu(row, '1')">{{row.nickname}}</a>
                    </template></vxe-column>
                    <vxe-column field="change_at_date"
                            title="动态变更时间"
                            min-width="110"></vxe-column>
                <vxe-column field="action"
                            title="操作"
                            align="center"
                            width="180"
                            fixed="right">
                    <template v-slot="{ row }">
                        <a @click="changeMenu(row, '8')">详情</a>
                    </template>
                </vxe-column>
            </vxe-table>
            <vxe-pager class="mt20"
                       border
                       size="medium"
                       :page-size="userFrom.limit"
                       :current-page="userFrom.page"
                       :total="total"
                       :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Total']"
                       @page-change="pageChange">
            </vxe-pager>
        </Card>
        <!-- 详情抽屉 -->
        <Drawer v-model="drawerVisible"
                width="65%"
                :closable="true"
                :title="drawerTitle">

            <div class="drawer-content">
                <div class="drawer-content-inner">
                    <div class="info-content">
                        <div class="info-header">
                            <Tabs active-key="客户动态"
                                  class="tabs">
                                <TabPane label="客户动态"
                                         key="客户动态">
                                    <div class="record-item"
                                         v-for="(item,index) in patientlog">
                                        <div class="record-date">{{index}}</div>
                                        <div class="record-item-content"
                                             v-for="(itm,index) in item">
                                            <div class="record-time">{{itm.time}}</div>
                                            <div class="record-content">
                                                <div class="record-title">{{itm.category}}</div>
                                                <div class="record-info">
                                                    <div class="record-info-item">{{itm.active_operator}}</div>
                                                    <div class="record-info-item">{{itm.active_text}}</div>
                                                </div>
                                                <div class="record-info">
                                                    <div class="record-info-item">{{itm.remark}}</div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- <div class="record-item-content">
                                            <div class="record-time">17:30</div>
                                            <div class="record-content">
                                                <div class="record-title">挂号记录</div>
                                                <div class="record-info">
                                                    <div class="record-info-item">预约：2025-6-1 8:30--12:00 中医科 陈刚 9元</div>
                                                </div>
                                            </div>
                                        </div> -->
                                    </div>

                                </TabPane>
                                <TabPane label="客户信息"
                                         key="客户信息">
                                    <div class="customer-info">

                                        <div class="info-section">
                                            <Button type="primary"
                                                    style="float: right;margin-bottom:10px"
                                                    @click="showEditModal">编辑</Button>
                                            <h3>基本信息</h3>

                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">就诊人ID：</span>
                                                    <span class="info-content">{{patientInfoList.card_no}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">出生日期：</span>
                                                    <span class="info-content">{{patientInfoList.birthday}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">家庭住址：</span>
                                                    <span class="info-content">{{patientInfoList.home_address}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">联系电话：</span>
                                                    <span class="info-content">{{patientInfoList.phone_number}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">身份证号码：</span>
                                                    <span class="info-content">{{patientInfoList.id_card}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">备注：</span>
                                                    <span class="info-content">{{patientInfoList.remark}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">特殊标注：</span>
                                                    <span class="info-content">{{patientInfoList.special_marking}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">民族：</span>
                                                    <span class="info-content">{{patientInfoList.nation}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">血型：</span>
                                                    <span class="info-content">{{patientInfoList.blood_type}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">婚姻状态：</span>
                                                    <span class="info-content">{{patientInfoList.marital_status}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">绑定用户：</span>
                                                    <span class="info-content">{{patientInfoList.nickname}}</span>
                                                </div>
                                                </Col>
                                            </Row>
                                        </div>

                                        <div class="info-section">
                                            <h3>联系人信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">姓名：</span>
                                                    <span class="info-content">{{contact_person.name}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">关系：</span>
                                                    <span class="info-content">{{contact_person.link}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">电话：</span>
                                                    <span class="info-content">{{contact_person.phone}}</span>
                                                </div>

                                                </Col>
                                            </Row>

                                            <h3 style="margin-top:20px">父亲信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">姓名：</span>
                                                    <span class="info-content">{{father_info.name}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">年龄：</span>
                                                    <span class="info-content">{{father_info.age}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">电话：</span>
                                                    <span class="info-content">{{father_info.phone}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">学历：</span>
                                                    <span class="info-content">{{father_info.edu}}</span>
                                                </div>
                                                </Col>
                                            </Row>

                                            <h3 style="margin-top:20px">母亲信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">姓名：</span>
                                                    <span class="info-content">{{mather_info.name}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">年龄：</span>
                                                    <span class="info-content">{{mather_info.age}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">电话：</span>
                                                    <span class="info-content">{{mather_info.phone}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">学历：</span>
                                                    <span class="info-content">{{mather_info.edu}}</span>
                                                </div>
                                                </Col>
                                            </Row>

                                            <h3 style="margin-top:20px">配偶信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">姓名：</span>
                                                    <span class="info-content">{{spouse_info.name}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">年龄：</span>
                                                    <span class="info-content">{{spouse_info.age}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">电话：</span>
                                                    <span class="info-content">{{spouse_info.phone}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">学历：</span>
                                                    <span class="info-content">{{spouse_info.edu}}</span>
                                                </div>
                                                </Col>
                                            </Row>

                                        </div>

                                        <div class="info-section">
                                            <h3>家庭信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">父母婚姻状态：</span>
                                                    <span class="info-content">{{family_info.parent_marital}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">是否为独生子女：</span>
                                                    <span class="info-content">{{family_info.is_only_child}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">家庭经济情况：</span>
                                                    <span class="info-content">{{family_info.family_financial}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">有无寄养经历：</span>
                                                    <span class="info-content">{{family_info.foster_care}}</span>
                                                </div>
                                                </Col>
                                            </Row>
                                        </div>

                                        <div class="info-section">
                                            <h3>其他信息</h3>
                                            <Row :gutter="24">
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">近一年的工作/学习成绩变化：</span>
                                                    <span class="info-content">{{patientInfoList.past_year}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">个人精神类疾病史：</span>
                                                    <span class="info-content">{{history_mental.option}}</span>
                                                    &nbsp;&nbsp;&nbsp;
                                                    <span class="info-content">{{history_mental.text?history_mental.text:''}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">家族精神类疾病史：</span>
                                                    <span class="info-content">{{family_history_mental.option}}</span>
                                                    &nbsp;&nbsp;&nbsp;
                                                    <span class="info-content">{{family_history_mental.text?family_history_mental.text:''}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">过往重大负面生活事件：</span>
                                                    <span class="info-content">{{life_import.option}}</span>
                                                    &nbsp;&nbsp;&nbsp;
                                                    <span class="info-content">{{life_import.text?life_import.text:''}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">求助主题：</span>
                                                    <span class="info-content">{{patientInfoList.help_topic}}</span>
                                                </div>
                                                </Col>
                                                <Col span="12">
                                                <div class="info-item">
                                                    <span class="info-label">交友情况：</span>
                                                    <span class="info-content">{{patientInfoList.friend_situation}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">是否曾有自伤或自杀：</span>
                                                    <span class="info-content">{{patientInfoList.is_self_harm}}</span>
                                                </div>
                                                <div class="info-item">
                                                    <span class="info-label">既往咨询经历：</span>
                                                    <span class="info-content">{{history_consultation.option}}</span>
                                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    <span class="info-content">{{history_consultation.text?history_consultation.text:''}}</span>
                                                </div>
                                                </Col>
                                            </Row>
                                        </div>
                                    </div>
                                </TabPane>
                                <TabPane label="挂号记录"
                                         key="挂号记录">
                                    <div class="registration-records">
                                        <Table :columns="registrationColumns"
                                               :data="registrationData"></Table>
                                    </div>
                                </TabPane>
                                <TabPane label="诊疗记录"
                                         key="诊疗记录">
                                    <div class="treatment-records">
                                        <Table :columns="treatmentColumns"
                                               :data="treatmentData"></Table>
                                    </div>
                                </TabPane>
                                <TabPane label="检验记录"
                                         key="检验记录">
                                    <Button type="primary"
                                            style="margin-left:20px"
                                            @click="openAddInspectionModal">添加</Button>
                                    <div class="inspection-records">
                                        <Table :columns="inspectionColumns"
                                               :data="inspectionData"></Table>

                                    </div>
                                </TabPane>
                                <TabPane label="测评记录"
                                         key="测评记录">
                                    <Button type="primary"
                                            style="margin-left:20px"
                                            @click="openAddAssessmentModal">添加</Button>
                                    <div class="assessment-records">
                                        <Table :columns="assessmentColumns"
                                               :data="assessmentData"></Table>
                                    </div>
                                </TabPane>
                                <TabPane label="心理咨询记录"
                                         key="心理咨询记录">
                                    <Button type="primary"
                                            style="margin-left:20px"
                                            @click="openAddConsultationModal">添加</Button>
                                    <div class="mental-health-records">
                                        <Table :columns="mentalHealthColumns"
                                               :data="mentalHealthData"></Table>
                                    </div>
                                </TabPane>
                                <TabPane label="跟进记录"
                                         key="跟进记录">
                                    <Button type="primary"
                                            style="margin-left:20px"
                                            @click="openAddFollowUpModal">添加</Button>
                                    <div class="follow-up-records">
                                        <Table :columns="followUpColumns"
                                               :data="followUpData"></Table>
                                    </div>
                                </TabPane>
                                <TabPane label="康讯记录"
                                         key="康讯记录">
                                    <div class="follow-up-records">
                                        <Table :columns="trainingList"
                                               :data="trainingListData"></Table>
                                    </div>
                                </TabPane>

                            </Tabs>
                        </div>

                    </div>
                </div>
            </div>

        </Drawer>
        
        <!-- 添加检验记录弹窗 -->
        <Modal v-model="addInspectionModal"
               width="500"
               title="添加检验记录"
               @on-cancel="cancelAddInspection">
            <Form ref="addInspectionForm"
                  :model="inspectionForm"
                  :label-width="100">
                <FormItem label="类型："
                          prop="type">
                    <Select v-model="inspectionForm.type"
                            style="width: 100%"
                            filterable>
                        <Option value="0">门诊</Option>
                    </Select>
                </FormItem>
                <FormItem label="检验时间："
                          prop="testing_time">
                    <DatePicker v-model="inspectionForm.testing_time"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择检验时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="检验项目："
                          prop="testing_project">
                    <Select v-model="inspectionForm.testing_project"
                            style="width: 100%"
                            filterable>
                        <Option v-for="(item, index) in inspectionProjects"
                                :key="index"
                                :value="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="检验结论："
                          prop="testing_result">
                    <Input v-model="inspectionForm.testing_result"
                           type="textarea"
                           :maxlength="100"
                           show-word-limit
                           placeholder="请输入检验结论，字数限制100字"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件："
                          prop="attachments">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in inspectionForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                     v-if="item.url">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeInspectionForm(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputInspection">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputInspection"
                                   @change="handleFileChangeInspection"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelAddInspection">取消</Button>
                <Button type="primary"
                        @click="confirmAddInspection">确定</Button>
            </div>
        </Modal>

        <!-- 编辑检验记录弹窗 -->
        <Modal v-model="editInspectionModal" width="500" title="编辑检验记录" @on-cancel="cancelEditInspection">
            <Form ref="editInspectionForm" :model="editInspectionForm" :label-width="100">
                <FormItem label="类型：" prop="type">
                    <Select v-model="editInspectionForm.type" style="width: 100%" filterable>
                        <Option value="0">门诊</Option>
                    </Select>
                </FormItem>
                <FormItem label="检验时间：" prop="testing_time">
                    <DatePicker v-model="editInspectionForm.testing_time" type="datetime" format="yyyy-MM-dd HH:mm" placeholder="选择检验时间" style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="检验项目：" prop="testing_project">
                    <Select v-model="editInspectionForm.testing_project" style="width: 100%" filterable>
                        <Option v-for="(item, index) in inspectionProjects" :key="index" :value="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="检验结论：" prop="testing_result">
                    <Input v-model="editInspectionForm.testing_result" type="textarea" :maxlength="100" show-word-limit placeholder="请输入检验结论，字数限制100字" style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件：" prop="attachments">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item" v-for="(item, index) in editInspectionForm.attachments" :key="index">
                                <div class="attachment-preview" v-if="item.url">
                                    <img :src="item.url" alt="预览" v-if="isImage(item.url)">
                                    <span class="file-name" v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button" @click.stop="removeEditInspectionAttachment(index)">
                                    <Icon type="md-close" size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area" @click="openFileInputEditInspection">
                            <Icon type="md-add"></Icon>
                            <input type="file" ref="fileInputEditInspection" @change="handleFileChangeEditInspection" accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt" style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelEditInspection">取消</Button>
                <Button type="primary" @click="updateInspection">更新</Button>
            </div>
        </Modal>

        <!-- 添加测评记录弹窗 -->
        <Modal v-model="addAssessmentModal"
               width="500"
               title="添加测评记录"
               @on-cancel="cancelAddAssessment">
            <Form ref="addAssessmentForm"
                  :model="assessmentForm"
                  :label-width="100">
                <FormItem label="类型："
                          prop="type">
                    <Select v-model="assessmentForm.type"
                            style="width: 100%"
                            filterable>
                        <Option value="0">门诊</Option>
                    </Select>
                </FormItem>
                <FormItem label="测评时间："
                          prop="evaluation_time">
                    <DatePicker v-model="assessmentForm.evaluation_time"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择测评时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="测评项目："
                          prop="evaluation_project">
                    <Select v-model="assessmentForm.evaluation_project"
                            style="width: 100%"
                            filterable>
                        <Option v-for="(item, index) in assessmentProjects"
                                :key="index"
                                :value="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="测评结论："
                          prop="evaluation_result">
                    <Input v-model="assessmentForm.evaluation_result"
                           type="textarea"
                           :maxlength="100"
                           show-word-limit
                           placeholder="请输入测评结论，字数限制100字"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件："
                          prop="attachments">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in assessmentForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                     v-if="item.url">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeassessmentForm(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputAssessment">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputAssessment"
                                   @change="handleFileChangeAssessment"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelAddAssessment">取消</Button>
                <Button type="primary"
                        @click="confirmAddAssessment">确定</Button>
            </div>
        </Modal>

        <!-- 编辑测评记录弹窗 -->
        <Modal v-model="editAssessmentModal"
               width="500"
               title="编辑测评记录"
               @on-cancel="cancelEditAssessment">
            <Form ref="editAssessmentForm"
                  :model="editAssessmentForm"
                  :label-width="100">
                <FormItem label="类型："
                          prop="type">
                    <Select v-model="editAssessmentForm.type"
                            style="width: 100%">
                        <Option value="0">门诊</Option>
                    </Select>
                </FormItem>
                <FormItem label="测评时间："
                          prop="evaluation_time">
                    <DatePicker v-model="editAssessmentForm.evaluation_time"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择测评时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="测评项目："
                          prop="evaluation_project">
                    <Select v-model="editAssessmentForm.evaluation_project"
                            style="width: 100%"
                            filterable>
                        <Option v-for="(item, index) in assessmentProjects"
                                :key="index"
                                :value='item'>{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem label="测评结论："
                          prop="evaluation_result">
                    <Input v-model="editAssessmentForm.evaluation_result"
                           type="textarea"
                           :maxlength="100"
                           show-word-limit
                           placeholder="请输入测评结论，字数限制100字"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件："
                          prop="attachments">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in editAssessmentForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                     v-if="item.url">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeEditAssessmentAttachment(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputEditAssessment">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputEditAssessment"
                                   @change="handleFileChangeEditAssessment"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelEditAssessment">取消</Button>
                <Button type="primary"
                        @click="updateAssessment">更新</Button>
            </div>
        </Modal>

        <!-- 查看测评记录弹窗 -->
        <Modal v-model="viewAssessmentModal"
               width="500"
               title="查看测评记录"
               :footer-hide="true">
            <Form ref="viewAssessmentForm"
                  :model="viewAssessmentForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Input v-model="viewAssessmentForm.type_text"
                           readonly></Input>
                </FormItem>
                <FormItem label="测评时间：">
                    <Input v-model="viewAssessmentForm.evaluation_time"
                           readonly></Input>
                </FormItem>
                <FormItem label="测评项目：">
                    <Input v-model="viewAssessmentForm.evaluation_project"
                           readonly></Input>
                </FormItem>
                <FormItem label="测评结论：">
                    <Input v-model="viewAssessmentForm.evaluation_result"
                           type="textarea"
                           readonly></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in viewAssessmentForm.attachments"
                                 :key="index">
                                <div class="attachment-preview" @click="handleAttachmentClick(item)">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </FormItem>
            </Form>
        </Modal>

        <!-- 添加咨询记录弹窗 -->
        <Modal v-model="addConsultationModal"
               width="600"
               title="添加咨询记录"
               @on-cancel="cancelAddConsultation">
            <Form ref="addConsultationForm"
                  :model="consultationForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Select v-model="consultationForm.type"
                            style="width: 100%">
                        <Option value="0">首次</Option>
                        <Option value="1">正式</Option>
                    </Select>
                </FormItem>
                <FormItem label="咨询时间：">
                    <DatePicker v-model="consultationForm.consultation_hours"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择咨询时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="咨询时长：">
                    <InputNumber :min="1"
                                 v-model="consultationForm.consultation_lenth"
                                 style="width: 100px; display: inline-block;" />
                    <span style="margin-left: 10px">分钟</span>
                </FormItem>
                <FormItem label="咨询方式：">
                    <Select v-model="consultationForm.consultation_type"
                            style="width: 100%">
                        <Option value="1">现场咨询</Option>
                        <Option value="2">电话咨询</Option>
                        <Option value="3">视频咨询</Option>
                        <Option value="4">微信咨询</Option>
                    </Select>
                </FormItem>
                <FormItem label="来访者情况：">
                    <Input v-model="consultationForm.visitor_description"
                           type="textarea"
                           :maxlength="2000"
                           show-word-limit
                           placeholder="请填写来访者基本信息、自述、他述等"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="咨询师评估：">
                    <Input v-model="consultationForm.consultant_assessment"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入咨询师评估内容"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="建议/计划：">
                    <Input v-model="consultationForm.suggestion"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入建议/计划内容"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="咨询内容："
                          v-if="consultationForm.type === '1'">
                    <Input v-model="consultationForm.consultation_content"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入咨询内容"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in consultationForm.attachments"
                                 :key="index">
                                <div class="attachment-preview">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeconsultationForm(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputConsultation">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputConsultation"
                                   @change="handleFileChangeConsultation"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelAddConsultation">取消</Button>
                <Button type="primary"
                        @click="confirmAddConsultation">确定</Button>
            </div>
        </Modal>

        <!-- 编辑咨询记录弹窗 -->
        <Modal v-model="editConsultationModal"
               width="600"
               title="编辑咨询记录"
               @on-cancel="cancelEditConsultation">
            <Form ref="editConsultationForm"
                  :model="editConsultationForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Select v-model="editConsultationForm.type"
                            style="width: 100%">
                        <Option value="0">首次</Option>
                        <Option value="1">正式</Option>
                    </Select>
                </FormItem>
                <FormItem label="咨询时间：">
                    <DatePicker v-model="editConsultationForm.consultation_hours"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择咨询时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="咨询时长：">
                    <InputNumber :min="1"
                                 v-model="editConsultationForm.consultation_lenth"
                                 style="width: 100px; display: inline-block;" />
                    <span style="margin-left: 10px">分钟</span>
                </FormItem>
                <FormItem label="咨询方式：">
                    <Select v-model="editConsultationForm.consultation_type"
                            style="width: 100%">
                        <Option value="1">现场咨询</Option>
                        <Option value="2">电话咨询</Option>
                        <Option value="3">视频咨询</Option>
                        <Option value="4">微信咨询</Option>
                    </Select>
                </FormItem>
                <FormItem label="来访者情况：">
                    <Input v-model="editConsultationForm.visitor_description"
                           type="textarea"
                           :maxlength="2000"
                           show-word-limit
                           placeholder="请填写来访者基本信息、自述、他述等"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="咨询师评估：">
                    <Input v-model="editConsultationForm.consultant_assessment"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入咨询师评估内容"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="建议/计划：">
                    <Input v-model="editConsultationForm.suggestion"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入建议/计划内容"
                           style="width: 100%"></Input>
                </FormItem>

                <FormItem label="咨询内容："
                          v-if="editConsultationForm.type === '1'">
                    <Input v-model="editConsultationForm.consultation_content"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入咨询内容"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in editConsultationForm.attachments"
                                 :key="index">
                                <div class="attachment-preview">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeEditConsultationForm(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputEditConsultation">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputEditConsultation"
                                   @change="handleFileChangeEditConsultation"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelEditConsultation">取消</Button>
                <Button type="primary"
                        @click="updateConsultation">更新</Button>
            </div>
        </Modal>

        <!-- 查看咨询记录弹窗 -->
        <Modal v-model="viewConsultationModal"
               width="600"
               title="查看咨询记录"
               :footer-hide="true">
            <Form ref="viewConsultationForm"
                  :model="viewConsultationForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Input v-model="viewConsultationForm.type"
                           readonly></Input>
                </FormItem>
                <FormItem label="咨询时间：">
                    <Input v-model="viewConsultationForm.consultation_hours"
                           readonly></Input>
                </FormItem>
                <FormItem label="咨询时长：">
                    <Input v-model="viewConsultationForm.consultation_lenth"
                           readonly
                           suffix="分钟"></Input>
                </FormItem>
                <FormItem label="咨询方式：">
                    <Input v-model="viewConsultationForm.consultation_type"
                           readonly></Input>
                </FormItem>
                <FormItem label="来访者情况：">
                    <Input v-model="viewConsultationForm.visitor_description"
                           type="textarea"
                           :maxlength="2000"
                           show-word-limit
                           readonly
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="咨询师评估：">
                    <Input v-model="viewConsultationForm.consultant_assessment"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           readonly
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="建议/计划：">
                    <Input v-model="viewConsultationForm.suggestion"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           readonly
                           style="width: 100%"></Input>
                </FormItem>

                <FormItem label="咨询内容："
                          v-if="viewConsultationForm.type_text == '正式'">
                    <Input v-model="viewConsultationForm.consultation_content"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           readonly
                           style="width: 100%"></Input>
                </FormItem>

                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in viewConsultationForm.attachments"
                                 :key="index">
                                <div class="attachment-preview" @click="handleAttachmentClick(item)">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </FormItem>
            </Form>
        </Modal>

        <!-- 图片预览模态框 -->
        <Modal v-model="showImageModal" width="800" :footer-hide="true">
            <div style="text-align: center;">
                <img :src="currentImageUrl" style="max-width: 100%; height: auto;">
            </div>
        </Modal>

        <!-- 添加跟进记录弹窗 -->
        <Modal v-model="addFollowUpModal"
               width="500"
               title="添加跟进记录"
               @on-cancel="cancelAddFollowUp">
            <Form ref="addFollowUpForm"
                  :model="followUpForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Select v-model="followUpForm.type"
                            style="width: 100%">
                        <Option value="0">回访</Option>
                        <Option value="1">邀约</Option>

                    </Select>
                </FormItem>
                <FormItem label="跟进时间：">
                    <DatePicker v-model="followUpForm.followTime"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择跟进时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="跟进内容：">
                    <Input v-model="followUpForm.followContent"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入跟进内容，字数限制1000字"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in followUpForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                     v-if="item.url">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeAttachment(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInput">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInput"
                                   @change="handleFileChange"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelAddFollowUp">取消</Button>
                <Button type="primary"
                        @click="confirmAddFollowUp">确定</Button>
            </div>
        </Modal>

        <!-- 编辑跟进记录弹窗 -->
        <Modal v-model="editFollowUpModal"
               width="500"
               title="编辑跟进记录">
            <Form ref="editFollowUpForm"
                  :model="editFollowUpForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Select v-model="editFollowUpForm.type"
                            style="width: 100%">
                        <Option value="0">回访</Option>
                        <Option value="1">邀约</Option>
                    </Select>
                </FormItem>
                <FormItem label="跟进时间：">
                    <DatePicker v-model="editFollowUpForm.followTime"
                                type="datetime"
                                format="yyyy-MM-dd HH:mm"
                                placeholder="选择跟进时间"
                                style="width: 100%"></DatePicker>
                </FormItem>
                <FormItem label="跟进内容：">
                    <Input v-model="editFollowUpForm.followContent"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入跟进内容，字数限制1000字"
                           style="width: 100%"></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in editFollowUpForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                     v-if="item.url">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                                <div class="remove-button"
                                     @click.stop="removeeditFollowUpForm(index)">
                                    <Icon type="md-close"
                                          size="18"></Icon>
                                </div>
                            </div>
                        </div>
                        <div class="upload-area"
                             @click="openFileInputedit">
                            <Icon type="md-add"></Icon>
                            <input type="file"
                                   ref="fileInputedit"
                                   @change="handleFileChanges"
                                   accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
                                   style="display: none;">
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button @click="cancelEditFollowUp">取消</Button>
                <Button type="primary"
                        @click="updateFollowUp">更新</Button>
            </div>
        </Modal>

        <!-- 查看跟进记录弹窗 -->
        <Modal v-model="viewFollowUpModal"
               width="500"
               title="查看跟进记录"
               :footer-hide="true">
            <Form ref="viewFollowUpForm"
                  :model="viewFollowUpForm"
                  :label-width="100">
                <FormItem label="类型：">
                    <Input v-model="viewFollowUpForm.type"
                           readonly></Input>
                </FormItem>
                <FormItem label="跟进时间：">
                    <Input v-model="viewFollowUpForm.followTime"
                           readonly></Input>
                </FormItem>
                <FormItem label="跟进内容：">
                    <Input v-model="viewFollowUpForm.followContent"
                           type="textarea"
                           :maxlength="1000"
                           show-word-limit
                           placeholder="请输入跟进内容，字数限制1000字"
                           style="width: 100%"
                           readonly></Input>
                </FormItem>
                <FormItem label="附件：">
                    <div class="attachment-container">
                        <div class="attachment-list">
                            <div class="attachment-item"
                                 v-for="(item, index) in viewFollowUpForm.attachments"
                                 :key="index">
                                <div class="attachment-preview"
                                      @click="handleAttachmentClick(item)">
                                    <img :src="item.url"
                                         alt="预览"
                                         v-if="isImage(item.url)">
                                    <span class="file-name"
                                          v-else>{{ getFileName(item.url) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </FormItem>
            </Form>
        </Modal>

        <!-- 跟进记录图片预览模态框 -->
        <Modal v-model="showImageModal" width="800" :footer-hide="true">
            <div style="text-align: center;">
                <img :src="currentImageUrl" style="max-width: 100%; height: auto;">
            </div>
        </Modal>


        <!-- 查看检验记录弹窗 -->
<Modal v-model="viewInspectionModal" width="500" title="查看检验记录" :footer-hide="true">
    <Form ref="viewInspectionForm" :model="viewInspectionForm" :label-width="100">
        <FormItem label="类型：">
            <Input v-model="viewInspectionForm.type_text" readonly></Input>
        </FormItem>
        <FormItem label="检验时间：">
            <Input v-model="viewInspectionForm.testing_time" readonly></Input>
        </FormItem>
        <FormItem label="检验项目：">
            <Input v-model="viewInspectionForm.testing_project" readonly></Input>
        </FormItem>
        <FormItem label="检验结论：">
            <Input v-model="viewInspectionForm.testing_result" type="textarea" readonly></Input>
        </FormItem>
        <FormItem label="附件：">
            <div class="attachment-container">
                <div class="attachment-list">
                    <div class="attachment-item" v-for="(item, index) in viewInspectionForm.attachments" :key="index">
                        <div class="attachment-preview" @click="handleAttachmentClick(item)">
                            <img :src="item.url" alt="预览" v-if="isImage(item.url)">
                            <span class="file-name" v-else>{{ getFileName(item.url) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </FormItem>
    </Form>
</Modal>

        <!--诊疗记录的查看-->
        <Modal v-model="treatmentDetailVisible"
               width="450"
               :closable="true"
               title="诊疗记录详情"
               @on-cancel="resetTreatmentDetail">
            <div class="info-content">
                <div class="info-section">
                    <div class="info-item">
                        <span class="info-label">主诉：</span>
                        <span class="info-content">{{ treatmentDetail.zs }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">现病史：</span>
                        <span class="info-content">{{ treatmentDetail.xbs }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">既往史：</span>
                        <span class="info-content">{{ treatmentDetail.jws }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">过敏史：</span>
                        <span class="info-content">{{ treatmentDetail.gms }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">辅助检查：</span>
                        <span class="info-content">{{ treatmentDetail.fzjc }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">体格检查：</span>
                        <span class="info-content">{{ treatmentDetail.tgjc }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">查体：</span>
                        <span class="info-content">{{ treatmentDetail.ct }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">特殊描述：</span>
                        <span class="info-content">{{ treatmentDetail.ts }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">诊断：</span>
                        <span class="info-content"
                              v-for="(itme,index) in treatmentDetail.zd ">{{itme}}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">处置方法：</span>
                        <span class="info-content">{{ treatmentDetail.czff }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">医嘱内容：</span>
                        <span class="info-content"
                              v-for="(itme,index) in treatmentDetail.yz ">{{itme}}</span>
                    </div>
                </div>
            </div>
            <div slot="footer">
                <Button type="primary"
                        @click="resetTreatmentDetail">关闭</Button>
            </div>
        </Modal>
        <!-- 编辑模态框 -->
        <Modal v-model="editModalVisible"
               title="编辑就诊人信息"
               @on-ok="saveEdit"
               width="60%"
               @on-cancel="cancelEdit"
               class-name="custom-modal">
            <Form ref="editForm"
                  :model="editFormData"
                  :label-width="100">
                <!-- 基本信息 -->
                <Divider orientation="left">基本信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                    <FormItem label="出生日期">
                        <DatePicker v-model="editFormData.birthday"
                                    type="date"
                                    placeholder="请选择出生日期"
                                    style="width: 100%"></DatePicker>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="婚姻状态">
                        <Select v-model="editFormData.marital_status"
                                placeholder="请选择婚姻状态"
                                style="width: 100%">
                            <Option value="良好">良好</Option>
                            <Option value="一般">一般</Option>
                            <Option value="离婚">离婚</Option>
                            <Option value="再婚">再婚</Option>
                            <Option value="丧偶">丧偶</Option>
                        </Select>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="家庭住址">
                        <Input v-model="editFormData.home_address"
                               placeholder="请输入家庭住址"
                               style="width: 100%"></Input>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="特殊标注">
                        <Input v-model="editFormData.special_marking"
                               placeholder="请输入特殊标注"
                               style="width: 100%"></Input>
                    </FormItem>
                </Col>
                <Col span="12">
                    <FormItem label="备注">
                        <Input v-model="editFormData.remark"
                               type="textarea"
                               :maxlength="500"
                               show-word-limit
                               placeholder="请输入备注，限制500字"
                               style="width: 100%"></Input>
                    </FormItem>
                </Col>
                
            </Row>



             <Divider orientation="left">联系人信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                     <FormItem label="姓名">
                        <Input v-model="editFormData.contact_person.name"
                               placeholder="请输入姓名"
                               style="width: 100%"></Input>
                    </FormItem>
                </Col>
                <Col span="12">
                      <FormItem label="电话">
                        <Input v-model="editFormData.contact_person.phone"
                               placeholder="请输入电话"
                               style="width: 100%"></Input>
                    </FormItem>
                     
                </Col>
                <Col span="12">
                   <FormItem label="关系">
                        <Input v-model="editFormData.contact_person.link"
                               placeholder="请输入关系"
                               style="width: 100%"></Input>
                    </FormItem>
                </Col>
                
            </Row>





             <Divider orientation="left">父亲信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                     <FormItem label="姓名">
                            <Input v-model="editFormData.father_info.name"
                                   placeholder="请输入姓名"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                <Col span="12">
                      <FormItem label="电话">
                            <Input v-model="editFormData.father_info.phone"
                                   placeholder="请输入电话"
                                   style="width: 100%"></Input>
                        </FormItem>
                     
                </Col>
                <Col span="12">
                    <FormItem label="年龄">
                            <Input v-model="editFormData.father_info.age"
                                   placeholder="请输入年龄"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                 <Col span="12">
                     <FormItem label="学历">
                            <Select v-model="editFormData.father_info.edu"
                                    placeholder="请选择学历"
                                    style="width: 100%">
                                <Option value="小学">小学</Option>
                                <Option value="初中">初中</Option>
                                <Option value="高中">高中</Option>
                                <Option value="中专">中专</Option>
                                <Option value="大专">大专</Option>
                                <Option value="本科">本科</Option>
                                <Option value="硕士">硕士</Option>
                                <Option value="硕士及以上">硕士及以上</Option>
                            </Select>
                        </FormItem>
                </Col>
                
            </Row>





            <Divider orientation="left">母亲信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                       <FormItem label="姓名">
                            <Input v-model="editFormData.mather_info.name"
                                   placeholder="请输入姓名"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                <Col span="12">
                      <FormItem label="年龄">
                            <Input v-model="editFormData.mather_info.age"
                                   placeholder="请输入年龄"
                                   style="width: 100%"></Input>
                        </FormItem>
                     
                </Col>
                <Col span="12">
                   <FormItem label="电话">
                            <Input v-model="editFormData.mather_info.phone"
                                   placeholder="请输入电话"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                 <Col span="12">
                     <FormItem label="学历">
                            <Select v-model="editFormData.mather_info.edu"
                                    placeholder="请选择学历"
                                    style="width: 100%">
                                <Option value="小学">小学</Option>
                                <Option value="初中">初中</Option>
                                <Option value="高中">高中</Option>
                                <Option value="中专">中专</Option>
                                <Option value="大专">大专</Option>
                                <Option value="本科">本科</Option>
                                <Option value="硕士">硕士</Option>
                                <Option value="硕士及以上">硕士及以上</Option>
                            </Select>
                        </FormItem>
                </Col>
            </Row>




            <Divider orientation="left">配偶信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                       <FormItem label="姓名">
                            <Input v-model="editFormData.spouse_info.name"
                                   placeholder="请输入姓名"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                <Col span="12">
                      <FormItem label="年龄">
                            <Input v-model="editFormData.spouse_info.age"
                                   placeholder="请输入年龄"
                                   style="width: 100%"></Input>
                        </FormItem>
                     
                </Col>
                <Col span="12">
                   <FormItem label="电话">
                            <Input v-model="editFormData.spouse_info.phone"
                                   placeholder="请输入电话"
                                   style="width: 100%"></Input>
                        </FormItem>
                </Col>
                 <Col span="12">
                     <FormItem label="学历">
                            <Select v-model="editFormData.spouse_info.edu"
                                    placeholder="请选择学历"
                                    style="width: 100%">
                                <Option value="小学">小学</Option>
                                <Option value="初中">初中</Option>
                                <Option value="高中">高中</Option>
                                <Option value="中专">中专</Option>
                                <Option value="大专">大专</Option>
                                <Option value="本科">本科</Option>
                                <Option value="硕士">硕士</Option>
                                <Option value="硕士及以上">硕士及以上</Option>
                            </Select>
                        </FormItem>
                </Col>
            </Row>



            
            <Divider orientation="left">家庭信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                      <FormItem label="父母婚姻状态">
                            <Select v-model="editFormData.family_info.parent_marital"
                                    placeholder="请选择父母婚姻状态"
                                    style="width: 100%">
                                <Option value="良好">良好</Option>
                                <Option value="一般">一般</Option>
                                <Option value="不好">不好</Option>
                            </Select>
                        </FormItem>
                </Col>
                <Col span="12">
                     <FormItem label="是否为独生子女">
                            <Select v-model="editFormData.family_info.is_only_child"
                                    placeholder="请选择是否为独生子女"
                                    style="width: 100%">
                                <Option value="是">是</Option>
                                <Option value="否">否</Option>
                            </Select>
                        </FormItem>
                     
                </Col>
                <Col span="12">
                   <FormItem label="家庭经济情况">
                            <Select v-model="editFormData.family_info.family_financial"
                                    placeholder="请选择家庭经济情况"
                                    style="width: 100%">
                                <Option value="富裕">富裕</Option>
                                <Option value="一般">一般</Option>
                                <Option value="贫困">贫困</Option>
                            </Select>
                        </FormItem>
                </Col>
                 <Col span="12">
                     <FormItem label="有无寄养经历">
                            <Select v-model="editFormData.family_info.foster_care"
                                    placeholder="请选择有无寄养经历"
                                    style="width: 100%">
                                <Option value="有">有</Option>
                                <Option value="无">无</Option>
                            </Select>
                        </FormItem>
                </Col>
            </Row>




             <Divider orientation="left">其他信息</Divider>
                <Row :gutter="16">
                <Col span="12">
                      <FormItem label="近一年的工作/学习成绩变化" >
                            <Select v-model="editFormData.past_year"
                                    placeholder="请选择"
                                    style="width: 100%">
                                <Option value="上升">上升</Option>
                                <Option value="稳定">稳定</Option>
                                <Option value="下降">下降</Option>
                                <Option value="大幅下降">大幅下降</Option>
                            </Select>
                        </FormItem>
                </Col>
                <Col span="12">
                      <FormItem label="交友情况">
                            <Select v-model="editFormData.friend_situation"
                                    placeholder="请选择交友情况"
                                    style="width: 100%">
                                <Option value="很多朋友">很多朋友</Option>
                                <Option value="一般多">一般多</Option>
                                <Option value="不多">不多</Option>
                            </Select>
                        </FormItem>
                     
                </Col>
               



                <Col span="12">
                  <FormItem label="个人精神类疾病史">
                            <Row>
                                <Col span="12">
                                    <Select v-model="editFormData.history_mental.option"
                                            placeholder="请选择"
                                            style="width: 100%">
                                        <Option value="有">有</Option>
                                        <Option value="无">无</Option>
                                    </Select>
                                </Col>
                                <Col span="12">
                                    <Input v-model="editFormData.history_mental.text"
                                           placeholder="请输入诊断机构、诊断情况"
                                           style="width: 100%" v-if="editFormData.history_mental.option === '有'"></Input>
                                </Col>
                            </Row>
                        </FormItem>
                </Col>
                 <Col span="12">
                  <FormItem label="是否曾有自伤或自杀">
                            <Select v-model="editFormData.is_self_harm"
                                    placeholder="请选择"
                                    style="width: 100%">
                                <Option value="有">有</Option>
                                <Option value="无">无</Option>
                            </Select>
                        </FormItem>
                </Col>




                <Col span="12">
                  <FormItem label="家族精神类疾病史">
                            <Row>
                                <Col span="12">
                                    <Select v-model="editFormData.family_history_mental.option"
                                            placeholder="请选择"
                                            style="width: 100%">
                                        <Option value="有">有</Option>
                                        <Option value="无">无</Option>
                                    </Select>
                                </Col>
                                <Col span="12">
                                    <Input v-model="editFormData.family_history_mental.text"
                                           placeholder="请输入关系和诊断情况"
                                           style="width: 100%" v-if="editFormData.family_history_mental.option === '有'"></Input>
                                </Col>
                            </Row>
                        </FormItem>
                </Col>
                 <Col span="12">
                     <FormItem label="既往咨询经历">
                            <Select v-model="editFormData.history_consultation.option"
                                    placeholder="请选择"
                                    style="width: 100%">
                                <Option value="有">有</Option>
                                <Option value="无">无</Option>
                            </Select>
                        </FormItem>
                </Col>




                <Col span="12">
                     <FormItem label="过往重大负面生活事件">
                            <Row>
                                <Col span="12">
                                    <Select v-model="editFormData.life_import.option"
                                            placeholder="请选择"
                                            style="width: 100%">
                                        <Option value="亲友生病">亲友生病</Option>
                                        <Option value="亲友亡故">亲友亡故</Option>
                                        <Option value="家人失业">家人失业</Option>
                                        <Option value="法律讼诉">法律讼诉</Option>
                                        <Option value="父母分居">父母分居</Option>
                                        <Option value="父母离异">父母离异</Option>
                                        <Option value="学业/事业遇挫">学业/事业遇挫</Option>
                                        <Option value="收到处分">收到处分</Option>
                                        <Option value="失恋">失恋</Option>
                                        <Option value="生病">生病</Option>
                                        <Option value="其他">其他</Option>
                                    </Select>
                                </Col>
                                <Col span="12">
                                    <Input v-model="editFormData.life_import.text"
                                           placeholder="其他请输入"
                                           style="width: 100%"   v-if="editFormData.life_import.option === '其他'"></Input>
                                </Col>
                            </Row>
                        </FormItem>
                </Col>
                
            </Row>
            

            <Divider orientation="left">求助主题</Divider>
                <Row :gutter="16">
                
                    <div class="info-section">
                        <FormItem>
                            <CheckboxGroup v-model="editFormData.help_topic">
                                <Checkbox label="自我探索">自我探索</Checkbox>
                                <Checkbox label="人际关系">人际关系</Checkbox>
                                <Checkbox label="学业问题">学业问题</Checkbox>
                                <Checkbox label="恋爱情感">恋爱情感</Checkbox>
                                <Checkbox label="亲子家庭">亲子家庭</Checkbox>
                                <Checkbox label="生涯发展">生涯发展</Checkbox>
                                <Checkbox label="情绪困扰">情绪困扰</Checkbox>
                                <Checkbox label="性别认同">性别认同</Checkbox>
                                <Checkbox label="适应问题">适应问题</Checkbox>
                                <Checkbox label="突发危机">突发危机</Checkbox>
                                <Checkbox label="身心健康">身心健康</Checkbox>
                                <Checkbox label="其他"  v-model="isOtherChecked">
                                    其他
                                    <Input v-model="editFormData.other_help_topic"
                                        placeholder="请输入"
                                        style="width: 150px; margin-left: 10px"  v-if="isOtherChecked"></Input>
                                </Checkbox>
                            </CheckboxGroup>
                        </FormItem>
                    </div>
                
            </Row>

            

            




                    



            

              
              
            </Form>
        </Modal>

        <!-- 用户标签 -->
        <Modal v-model="labelListShow"
               scrollable
               title="选择用户标签"
               :closable="true"
               width="540"
               :footer-hide="true"
               :mask-closable="false">
            <labelList ref="labelList"
                       @activeData="activeData"
                       @close="labelListClose"></labelList>
        </Modal>
        <!-- 编辑表单 积分余额-->
        <edit-from ref="edits"
                   :FromData="FromData"
                   :userEdit="1"
                   @submitFail="submitFail"></edit-from>
        <!-- 发送优惠券-->
        <send-from ref="sends"
                   :is-all="isAll"
                   :where="userFrom"
                   :userIds="checkUidList.join(',')"></send-from>
        <!-- 会员详情-->
        <user-details ref="userDetails"
                      :group-list="groupList"></user-details>
        <!--发送图文消息 -->
        <Modal v-model="modal13"
               scrollable
               title="发送消息"
               width="1200"
               height="800"
               footer-hide
               class="modelBox">
            <news-category v-if="modal13"
                           :isShowSend="isShowSend"
                           :is-all="isAll"
                           :where="userFrom"
                           :userIds="checkUidList.join(',')"
                           :scrollerHeight="scrollerHeight"
                           :contentTop="contentTop"
                           :contentWidth="contentWidth"
                           :maxCols="maxCols"></news-category>
        </Modal>
        <!--修改推广人-->
        <Modal v-model="promoterShow"
               scrollable
               title="修改推广人"
               class="order_box"
               :closable="false">
            <Form ref="formInline"
                  :model="formInline"
                  :label-width="100"
                  @submit.native.prevent>
                <FormItem label="用户头像："
                          prop="image">
                    <div class="picBox"
                         @click="customer">
                        <div class="pictrue"
                             v-if="formInline.image">
                            <img v-lazy="formInline.image" />
                        </div>
                        <div class="upLoad acea-row row-center-wrapper"
                             v-else>
                            <Icon type="ios-camera-outline"
                                  size="26" />
                        </div>
                    </div>
                </FormItem>
            </Form>
            <div slot="footer">
                <Button type="primary"
                        @click="putSend('formInline')">提交</Button>
                <Button @click="cancel('formInline')">取消</Button>
            </div>
        </Modal>
        <Modal v-model="customerShow"
               scrollable
               title="请选择商城用户"
               :closable="false"
               width="900">
            <customerInfo v-if="customerShow"
                          @imageObject="imageObject"></customerInfo>
        </Modal>
        <Modal v-model="labelShow"
               scrollable
               title="选择用户标签"
               :closable="true"
               width="540"
               :footer-hide="true">
            <userLabel :uid="labelActive.uid"
                       @close="labelClose"></userLabel>
        </Modal>
        <!-- 批量设置 -->
        <Modal v-model="batchModal"
               title="批量设置"
               width="750"
               class-name="batch-modal"
               @on-visible-change="batchVisibleChange">
            <Alert show-icon>每次只能修改一项，如需修改多项，请多次操作。</Alert>
            <Row type="flex"
                 align="middle">
                <Col span="4">
                <Menu :active-name="menuActive"
                      width="auto"
                      @on-select="menuSelect">
                    <MenuItem :name="1">用户分组</MenuItem>
                    <MenuItem :name="2">用户标签</MenuItem>
                    <MenuItem :name="3">用户等级</MenuItem>
                    <MenuItem :name="4">积分余额</MenuItem>
                    <MenuItem :name="5">赠送会员</MenuItem>
                    <MenuItem :name="6">上级推广人</MenuItem>
                </Menu>
                </Col>
                <Col span="20">
                <Form :model="batchData"
                      :label-width="122">
                    <FormItem v-if="menuActive === 1"
                              label="用户分组：">
                        <Select v-model="batchData.group_id">
                            <Option v-for="item in groupList"
                                    :key="item.id"
                                    :value="item.id">{{ item.group_name }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem v-if="menuActive === 2"
                              label="用户标签：">
                        <div class="select-tag"
                             @click="openLabelList">
                            <div v-if="batchLabel.length">
                                <Tag v-for="item in batchLabel"
                                     :key="item.id"
                                     closable
                                     @on-close="tagClose(item.id)">{{ item.label_name }}</Tag>
                            </div>
                            <span v-else
                                  class="placeholder">请选择</span>
                            <Icon type="ios-arrow-down" />
                        </div>
                    </FormItem>
                    <FormItem v-if="menuActive === 3"
                              label="用户等级：">
                        <Select v-model="batchData.level_id">
                            <Option v-for="item in levelList"
                                    :key="item.id"
                                    :value="item.id">{{ item.name }}</Option>
                        </Select>
                    </FormItem>
                    <FormItem v-if="menuActive === 4"
                              label="修改余额：">
                        <RadioGroup v-model="batchData.money_status">
                            <Radio :label="1">增加</Radio>
                            <Radio :label="2">减少</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem v-if="menuActive === 4"
                              label="余额：">
                        <InputNumber v-model="batchData.money"
                                     :min="0"
                                     :max="999999"></InputNumber>
                    </FormItem>
                    <FormItem v-if="menuActive === 4"
                              label="修改积分：">
                        <RadioGroup v-model="batchData.integration_status">
                            <Radio :label="1">增加</Radio>
                            <Radio :label="2">减少</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem v-if="menuActive === 4"
                              label="积分：">
                        <InputNumber v-model="batchData.integration"
                                     :min="0"
                                     :max="999999"></InputNumber>
                    </FormItem>
                    <FormItem v-if="menuActive === 5"
                              label="修改时长：">
                        <RadioGroup v-model="batchData.days_status">
                            <Radio :label="1">增加</Radio>
                            <Radio :label="2">减少</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem v-if="menuActive === 5"
                              label="修改时长(天)：">
                        <InputNumber v-model="batchData.day"
                                     :min="0"
                                     :max="999999"></InputNumber>
                    </FormItem>
                    <FormItem v-if="menuActive === 6"
                              label="上级推广员：">
                        <Input :value="spread_name"
                               placeholder="请选择"
                               icon="ios-arrow-down"
                               @on-click="customer"
                               @on-focus="customer"></Input>
                    </FormItem>
                </Form>
                </Col>
            </Row>
            <div slot="footer">
                <Button @click="cancelBatch">取消</Button>
                <Button type="primary"
                        @click="saveBatch">保存</Button>
            </div>
        </Modal>
    </div>
</template>

<script>
    import util from '@/libs/util';
    import axios from "axios";
    import { formatDate } from '@/utils/validate';
    import userLabel from "../../../components/userLabel";
    import labelList from "@/components/labelList";
    import { mapState } from "vuex";
    import expandRow from "./tableExpand.vue";
    import {
        updatePatientInfo,
        getInspectionDetailsApi,
        updateInspectionApi,
        addInspectionApi,
        getInspectionProjectsApi,
        updateAssessmentApi,
        getAssessmentDetailsApi,
        addAssessmentApi,
        getAssessmentProjectsApi,
        addfollow,
        addconsultation,
        updatefollow,
        updateConsultationApi,
        getConsultationDetailsApi,
        userList,
        patientList,
        patientlogList,
        patientInfo,
        registeredorderList,
        medicalrecordList,
        testingrecordList,
        evaluationrecordList,
        consultationList,
        followList,
        getUserData,
        isShowApi,
        editOtherApi,
        giveLevelApi,
        userSetGroup,
        userGroupApi,
        levelListApi,
        userSetLabelApi,
        userLabelApi,
        userSynchro,
        getUserSaveForm,
        giveLevelTimeApi,
        extendInfo,
        batchProcess,
        gethealthtrainingList
    } from "@/api/user";
    import { agentSpreadApi } from "@/api/agent";
    import { staffListInfo } from "@/api/store";
    import editFrom from "../../../components/from/from";
    import sendFrom from "@/components/sendCoupons/index";
    import userDetails from "./handle/userDetails";
    import newsCategory from "@/components/newsCategory/index";
    import city from "@/utils/city";
    import customerInfo from "@/components/customerInfo";
    import cookies from '../../../libs/util.cookies';
    export default {
        name: "user_list",
        filters: {
            formatDate (time) {
                if (time !== 0) {
                    let date = new Date(time * 1000);
                    return formatDate(date, 'yyyy-MM-dd hh:mm');
                }
            }
        },
        components: {
            expandRow,
            editFrom,
            sendFrom,
            userDetails,
            newsCategory,
            customerInfo,
            userLabel,
            labelList,
        },
        data () {
            return {
            showImageModal: false,
        currentImageUrl: '',
                 isOtherChecked: false, // 控制“其他”输入框的显示状态
                /**
                 * 就诊人信息
                 */
                editFormData: {
                    other_help_topic: '', // “其他”输入框的值
                //生日
                infoId:0,
                birthday: '',
                //家庭住址
                home_address: '',
                //婚姻状态
                marital_status: '',
                special_marking: '',
                remark: '',
                contact_person:[],
                father_info:[],
                mather_info:[],
                spouse_info:[],
                family_info:[],
                history_mental:[],
                family_history_mental:[],
                history_consultation:[],
                life_import:[],
            },
         viewInspectionModal: false,
        viewInspectionForm: {
            type:'',
            type_text: '',
            testing_time: '',
            testing_project: '',
            testing_result: '',
            attachments: []
        },
        editInspectionModal: false,
        editInspectionForm: {
            id: '', // 检验记录ID
            type: '', // 类型
            testing_time: '', // 检验时间
            testing_project: '', // 检验项目
            testing_result: '', // 检验结论
            attachments: [] // 附件
        },
        inspectionProjects: [], // 检验项目列表
                // 添加检验记录弹窗
                addInspectionModal: false,
                inspectionForm: {
                    type: '', // 类型
                    testing_time: '', // 检验时间
                    testing_project: '', // 检验项目
                    testing_result: '', // 检验结论
                    attachments: [] // 附件
                },
                inspectionProjects: [], // 检验项目列表
                viewAssessmentModal: false,
                viewAssessmentForm: {
                    id: '',
                    type: '',
                    evaluation_time: '',
                    evaluation_project: '',
                    evaluation_result: '',
                    attachments: []
                },
                editAssessmentModal: false,
                editAssessmentForm: {
                    id: '', // 测评记录ID
                    type: '', // 类型
                    evaluation_time: '', // 测评时间
                    evaluation_project: '', // 测评项目
                    evaluation_result: '', // 测评结论
                    attachments: [] // 附件
                },
                addAssessmentModal: false,
                assessmentForm: {
                    type: '', // 类型
                    evaluation_time: '', // 测评时间
                    evaluation_project: '', // 测评项目
                    evaluation_result: '', // 测评结论
                    attachments: [], // 附件
                },
                assessmentProjects: [], // 测评项目列表
                viewConsultationModal: false,
                viewConsultationForm: {
                    id: '',
                    type: '',
                    consultation_hours: '',
                    consultation_lenth: '',
                    consultation_type: '',
                    visitor_description: '',
                    consultant_assessment: '',
                    suggestion: '',
                    attachments: []
                },
                addConsultationModal: false,
                consultationForm: {
                    type: '0', // 默认初访
                    consultation_hours: '', // 咨询时间
                    consultation_lenth: '', // 咨询时长，默认60分钟
                    consultation_type: 1, // 默认现场咨询
                    visitor_description: '', // 来访者情况
                    consultant_assessment: '', // 咨询师评估
                    suggestion: '', // 建议/计划
                    attachments: [] // 附件
                },
                editConsultationModal: false,
                editConsultationForm: {
                    id: '', // 咨询记录ID
                    type: '', // 类型
                    consultation_hours: '', // 咨询时间
                    consultation_lenth: '', // 咨询时长，默认60分钟
                    consultation_type: 1, // 咨询方式
                    visitor_description: '', // 来访者情况
                    consultant_assessment: '', // 咨询师评估
                    suggestion: '', // 建议/计划
                    attachments: [] // 附件
                },
                viewFollowUpModal: false,
                viewFollowUpForm: {
                    id: '',
                    type: '',
                    followTime: '',
                    followContent: '',
                    attachments: []
                },
                editFollowUpModal: false,
                editFollowUpForm: {
                    id: '', // 跟进记录ID
                    type: '',
                    followTime: '',
                    followContent: '',
                    attachments: []
                },
                pushcard_no: '',
                addFollowUpModal: false,
                followUpForm: {
                    type: '',
                    followTime: '',
                    followContent: '',
                    attachments: []
                },
                treatmentDetailVisible: false,
                treatmentDetail: {},
                patientlog: [],
                patientInfoList: '',
                contact_person: [],
                father_info: [],
                mather_info: [],
                spouse_info: [],
                family_info: [],
                history_mental: [],
                family_history_mental: [],
                history_consultation: [],
                life_import: [],
                editModalVisible: false,
               
                drawerVisible: false,
                // 其他数据...
                currentPatientName: '', // 当前就诊人名称
                currentPatientAge: '',   // 当前就诊人年龄
                followUpColumns: [
                    {
                        title: '类型',
                        key: 'type_text'
                    },
                    {
                        title: '时间',
                        key: 'follow_time'
                    },
                    {
                        title: '人员',
                        key: 'operator_name'
                    },
                    {
                        title: '描述',
                        key: 'follow_content'
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)'
                                    },
                                    on: {
                                        click: () => {
                                            this.viewFollowUpDetails(params.row)
                                        }
                                    }
                                }, '查看'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px'
                                    },
                                    on: {
                                        click: () => {
                                            this.editFollowUp(params.row)
                                        }
                                    }
                                }, '编辑'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px; color: red'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteFollowUp(params.row, '删除跟进记录', 0)
                                        }
                                    }
                                }, '删除')
                            ]);
                        }
                    }
                ],

                trainingList: [
                    {
                        title: '康训时间',
                        key: 'training_time'
                    },
                    {
                        title: '就诊人',
                        key: 'patient_name'
                    },
                    {
                        title: '性别',
                        key: 'gender'
                    },
                    {
                        title: '康训项目',
                        key: 'training_project_text'
                    },
                    {
                        title: '备注',
                        key: 'remark'
                    },
                    {
                        title: '康训师',
                        key: 'operator_name'
                    },
                    
                ],
                trainingListData:[],     
                followUpData: [],
                mentalHealthColumns: [
                    {
                        title: '类型',
                        key: 'type_text'
                    },
                    {
                        title: '咨询时间',
                        key: 'consultation_hours'
                    },
                    {
                        title: '咨询时长',
                        key: 'consultation_lenth'
                    },
                    {
                        title: '咨询方式',
                        key: 'consultation_type_text'
                    },
                    {
                        title: '咨询师',
                        key: 'consultant_doctor'
                    },
                    // {
                    //     title: '状态',
                    //     key: 'status'
                    // },
                    // {
                    //     title: '诊断',
                    //     key: 'diagnosis'
                    // },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)'
                                    },
                                    on: {
                                        click: () => {
                                            this.getMentalHealthDetailsss(params.row)
                                        }
                                    }
                                }, '查看'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px'
                                    },
                                    on: {
                                        click: () => {
                                            this.editMentalHealth(params.row)
                                        }
                                    }
                                }, '编辑'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px; color: red'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteMentalHealth(params.row, '心理咨询记录', 0)
                                        }
                                    }
                                }, '删除')
                            ]);
                        }
                    }
                ],
                mentalHealthData: [],
                // 其他数据...
                assessmentColumns: [
                    {
                        title: '类型',
                        key: 'type_text'
                    },
                    {
                        title: '测评时间',
                        key: 'evaluation_time'
                    },
                    {
                        title: '测评人员',
                        key: 'evaluation_doctor'
                    },
                    {
                        title: '测评项目',
                        key: 'evaluation_project'
                    },
                    {
                        title: '测评结论',
                        key: 'evaluation_result'
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)'
                                    },
                                    on: {
                                        click: () => {
                                            this.viewAssessmentDetailsss(params.row)
                                        }
                                    }
                                }, '查看'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px'
                                    },
                                    on: {
                                        click: () => {
                                            this.editAssessment(params.row)
                                        }
                                    }
                                }, '编辑'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px; color: red'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteAssessment(params.row, '删除测评记录', 0)
                                        }
                                    }
                                }, '删除')






                            ])
                        }
                    }
                ],
                assessmentData: [],
                inspectionColumns: [
                    {
                        title: '类型',
                        key: 'type_text'
                    },
                    {
                        title: '检验时间',
                        key: 'testing_time'
                    },
                    {
                        title: '检验人员',
                        key: 'testing_doctor'
                    },
                    {
                        title: '检验项目',
                        key: 'testing_project'
                    },
                    {
                        title: '检验结论',
                        key: 'testing_result'
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('div', [
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)'
                                    },
                                    on: {
                                        click: () => {
                                            this.viewInspectionDetailsss(params.row)
                                        }
                                    }
                                }, '查看'),
                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px'
                                    },
                                    on: {
                                        click: () => {
                                            this.editInspection(params.row)
                                        }
                                    }
                                }, '编辑'),

                                h('a', {
                                    attrs: {
                                        href: 'javascript:void(0)',
                                        style: 'margin-left: 10px; color: red'
                                    },
                                    on: {
                                        click: () => {
                                            this.deleteInspection(params.row, '删除检验记录', 0)
                                        }
                                    }
                                }, '删除')



                            ])
                        }
                    }
                ],
                inspectionData: [],
                treatmentColumns: [
                    {
                        title: '类型',
                        key: 'type_text',
                    },
                    {
                        title: '就诊时间',
                        key: 'admission_date'
                    },

                    {
                        title: '科室',
                        key: 'admission_dept'
                    },
                    {
                        title: '主诊医生',
                        key: 'admission_doctor'
                    },
                    {
                        title: '状态',
                        key: 'status'
                    },
                    {
                        title: '诊断',
                        key: 'diagnosis'
                    },
                    {
                        title: '操作',
                        key: 'action',
                        render: (h, params) => {
                            return h('a', {
                                attrs: {
                                    href: 'javascript:void(0)'
                                },
                                on: {
                                    click: () => {
                                        this.viewTreatmentDetails(params.row)
                                    }
                                }
                            }, '查看')
                        }
                    }
                ],
                treatmentData: [],
                registrationColumns: [
                    {
                        title: '挂号时间',
                        key: 'created_at'
                    },
                    {
                        title: '就诊科室',
                        key: 'dept_name'
                    },
                    {
                        title: '就诊医生',
                        key: 'doctor_name'
                    },
                    {
                        title: '实付金额（元）',
                        key: 'recei_money'
                    },
                    {
                        title: '预约时间',
                        key: 'treatment_date'
                    },
                    {
                        title: '状态',
                        key: 'status_text'
                    }
                ],
                registrationData: [],
                dataLabel: [],
                labelListShow: false,
                labelShow: false,
                customerShow: false,
                promoterShow: false,
                labelActive: {
                    uid: 0,
                },
                formInline: {
                    uid: 0,
                    spread_uid: 0,
                    image: "",
                },
                options: {
                    shortcuts: [
                        {
                            text: "今天",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    new Date(
                                        new Date().getFullYear(),
                                        new Date().getMonth(),
                                        new Date().getDate()
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "昨天",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    start.setTime(
                                        new Date(
                                            new Date().getFullYear(),
                                            new Date().getMonth(),
                                            new Date().getDate() - 1
                                        )
                                    )
                                );
                                end.setTime(
                                    end.setTime(
                                        new Date(
                                            new Date().getFullYear(),
                                            new Date().getMonth(),
                                            new Date().getDate() - 1
                                        )
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "最近7天",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    start.setTime(
                                        new Date(
                                            new Date().getFullYear(),
                                            new Date().getMonth(),
                                            new Date().getDate() - 6
                                        )
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "最近30天",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    start.setTime(
                                        new Date(
                                            new Date().getFullYear(),
                                            new Date().getMonth(),
                                            new Date().getDate() - 29
                                        )
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "上月",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                const day = new Date(start.getFullYear(), start.getMonth(), 0).getDate();
                                start.setTime(
                                    start.setTime(
                                        new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1)
                                    )
                                );
                                end.setTime(
                                    end.setTime(
                                        new Date(new Date().getFullYear(), new Date().getMonth() - 1, day)
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "本月",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    start.setTime(
                                        new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                                    )
                                );
                                return [start, end];
                            },
                        },
                        {
                            text: "本年",
                            value () {
                                const end = new Date();
                                const start = new Date();
                                start.setTime(
                                    start.setTime(new Date(new Date().getFullYear(), 0, 1))
                                );
                                return [start, end];
                            },
                        },
                    ],
                },
                collapse: false,
                headeType: "-1",
                headeNum: [
                    { type: "-1", name: "全部" },
                    // { type: "wechat", name: "微信公众号" },
                    // { type: "routine", name: "微信小程序" },
                    // { type: "h5", name: "H5" },
                    // { type: "pc", name: "PC" },
                    // { type: "app", name: "APP" },
                    // { type: "cashier", name: "收银台" },
                ],
                address: [],
                addresData: city,
                isShowSend: true,
                modal13: false,
                maxCols: 4,
                scrollerHeight: "600",
                contentTop: "130",
                contentWidth: "98%",
                grid2: {
                    xl: 18,
                    lg: 16,
                    md: 12,
                    sm: 24,
                    xs: 24,
                },
                loading: false,
                total: 0,
                userFrom: {
                    // label_id: "",
                    // user_type: "",
                    // status: "",
                    // sex: "",
                    // is_promoter: "",
                    // country: "",
                    // isMember: "",
                    // pay_count: "",
                    // user_time_type: "",
                    // user_time: "",
                    // nickname: "",
                    // province: "",
                    // city: "",
                    page: 1,
                    limit: 15,
                    // level: "",
                    // group_id: "",
                    field_key: "",
                    // store_id: '',

                    card_no: '',
                    patient_name: '',
                    id_card: '',
                    phone_number: ''
                },
                field_key: "",
                level: "",
                group_id: "",
                label_id: "",
                user_time_type: "",
                pay_count: "",
                userLists: [],
                FromData: null,
                selectionList: [],
                user_ids: "",
                selectedData: [],
                timeVal: [],
                array_ids: [],
                groupList: [],
                levelList: [],
                labelFrom: {
                    page: 1,
                    limit: "",
                },
                labelLists: [],
                display: "none",
                checkBox: false,
                selectionCopy: [],
                isCheckBox: false,
                isAll: 0,
                userId: 0,
                checkUidList: [],
                batchModal: false,
                menuActive: 1,
                batchLabel: [],
                batchData: {
                    group_id: 0,
                    label_id: [],
                    level_id: 0,
                    money_status: 0,
                    money: 0,
                    integration_status: 0,
                    integration: 0,
                    days_status: 1,
                    day: 0,
                    spread_uid: '',
                },
                spread_name: '',
                storeList: []
            };
        },
        watch: {
            //个人精神病史
            'editFormData.history_mental.option': function (newVal) {
                if (newVal === '无') {
                    this.editFormData.history_mental.text = ''; // 当选择“无”时清空输入框
                }
            },
            //家族精神类疾病史
            'editFormData.family_history_mental.option': function (newVal) {
                if (newVal === '无') {
                    this.editFormData.family_history_mental.text = ''; // 当选择“无”时清空输入框
                }
            },

            //过往重大负面生活事件
            'editFormData.life_import.option': function (newVal) {
                console.log(newVal,'罗鑫大队')
                if (newVal !== '其他') {
                    this.editFormData.life_import.text = ''; // 当选择“其他”时清空输入框
                }
            },

            isOtherChecked(newVal) {
                // 如果取消勾选“其他”，清空输入框的值
                if (!newVal) {
                    this.editFormData.other_help_topic = '';
                }
            },
            selectionList (value) {
                let arr = value.map((item) => item.uid);
                this.array_ids = arr;
                this.user_ids = arr.join();
            },
            userLists: {
                deep: true,
                handler (value) {
                    value.forEach((item) => {
                        this.selectionList.forEach((itm) => {
                            if (itm.uid === item.uid) {
                                item.checkBox = true;
                            }
                        });
                    });
                    const arr = this.userLists.filter((item) => item.checkBox);
                    if (this.userLists.length) {
                        this.checkBox = this.userLists.length === arr.length;
                    } else {
                        this.checkBox = false;
                    }
                },
            },
        },
        computed: {
            ...mapState("admin/layout", ["isMobile"]),
            labelWidth () {
                return this.isMobile ? undefined : 100;
            },
            labelPosition () {
                return this.isMobile ? "top" : "right";
            },
            drawerTitle () {
                // return `${this.currentPatientName} (${this.currentPatientAge}岁)`;
                return `${this.currentPatientName}`;
            }
        },
        created () {
            this.allStore();
            this.getList();
        },
        mounted () {
            this.userGroup();
            this.levelLists();
            this.groupLists();
        },
        methods: {
        // 其他方法保持不变
    handleAttachmentClick(item) {
        if (this.isImage(item.url)) {
            this.showImageModal = true;
            this.currentImageUrl = item.url;
        } else {
            this.downloadFile(item.url);
        }
    },
    downloadFile(url) {
        const link = document.createElement('a');
        link.href = url;
        link.download = this.getFileName(url);
        link.click();
    },
             // 编辑检验记录
        editInspection(row) {
            this.editInspectionModal = true;
            this.getInspectionDetails(row.id);
        },
    // 获取检验记录详情
        getInspectionDetails(id) {
            getInspectionDetailsApi(id).then(res => {
                if (res.status === 200) {
                    const data = res.data;
                    this.editInspectionForm = {
                        id: data.id,
                        type: String(data.type),
                        testing_time: new Date(data.testing_time),
                        testing_project: data.testing_project,
                        testing_result: data.testing_result,
                        attachments: data.files ? data.files.map(item => ({
                            url: item.att_dir,
                            name: item.filename,
                            att_id: item.att_id
                        })) : []
                    };
                    // 确保检验项目列表已加载
                    if (!this.inspectionProjects.length) {
                        this.getInspectionProjects();
                    }
                }
            }).catch(err => {
                this.$Message.error('获取检验记录详情失败');
            });
        },
        // 更新检验记录
        updateInspection() {
            // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
            const formattedDate = this.formatDate(this.editInspectionForm.testing_time);
            // 这里添加提交数据的逻辑
            const formData = {
                id: this.editInspectionForm.id,
                card_no: this.pushcard_no,
                type: this.editInspectionForm.type,
                testing_time: formattedDate,
                testing_project: this.editInspectionForm.testing_project,
                testing_result: this.editInspectionForm.testing_result,
                files: this.editInspectionForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
            }
            updateInspectionApi(formData).then(res => {
                if (res.status === 200) {
                    this.$Message.success(res.msg);
                    this.editInspectionModal = false;
                    this.gettestingrecordList(this.pushcard_no);
                } else {
                    this.$Message.error('更新失败：' + res.msg);
                }
            }).catch(err => {
                this.$Message.error('更新出错，请稍后再试');
                console.error(err);
            });
        },
    // 打开文件选择器（编辑检验记录）
    openFileInputEditInspection() {
        this.$refs.fileInputEditInspection.click();
    },
    // 处理文件上传（编辑检验记录）
    handleFileChangeEditInspection(e) {
        const file = e.target.files[0];
        if (!file) return;

        this.uploadFileEditInspection(file);
    },
    async uploadFileEditInspection(file) {
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'Authori-zation': 'Bearer ' + util.cookies.get('token')
                }
            });
            if (response.data) {
                this.editInspectionForm.attachments.push({
                    url: response.data.data.url,
                    name: response.data.data.filename,
                    att_id: response.data.data.att_id
                });
                this.$refs.fileInputEditInspection.value = ''; // 清空文件输入框的值
            } else {
                this.$Message.error('上传失败：' + response.data);
            }
        } catch (error) {
            this.$Message.error('上传出错，请稍后再试');
            console.error(error);
        }
    },
    // 删除附件（编辑检验记录）
    removeEditInspectionAttachment(index) {
        this.editInspectionForm.attachments.splice(index, 1);
    },
    // 取消编辑检验记录
    cancelEditInspection() {
        this.editInspectionModal = false;
    },
            // 打开添加检验记录弹窗
            openAddInspectionModal () {
                this.addInspectionModal = true;
                this.inspectionForm = {
                    type: '', // 类型
                    testing_time: '', // 检验时间
                    testing_project: '', // 检验项目
                    testing_result: '', // 检验结论
                    attachments: [] // 附件
                };
                this.getInspectionProjects(); // 获取检验项目列表
            },
            // 获取检验项目列表
            getInspectionProjects () {
                getInspectionProjectsApi().then(res => {
                    if (res.status === 200) {
                        this.inspectionProjects = res.data;
                    }
                }).catch(err => {
                    this.$Message.error('获取检验项目列表失败');
                });
            },
            // 确认添加检验记录
            confirmAddInspection () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                const formattedDate = this.formatDate(this.inspectionForm.testing_time);
                // 这里添加提交数据的逻辑
                const formData = {
                    card_no: this.pushcard_no,
                    type: this.inspectionForm.type,
                    testing_time: formattedDate,
                    testing_project: this.inspectionForm.testing_project,
                    testing_result: this.inspectionForm.testing_result,
                    files: this.inspectionForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                };
                console.log(formData, '检验记录提交数据');
                addInspectionApi(formData).then(res => {
                    if (res.status === 200) {
                        this.$Message.success(res.msg);
                        this.addInspectionModal = false;
                        this.gettestingrecordList(this.pushcard_no);
                    }
                }).catch(error => {
                    this.$Message.error('添加失败，请稍后再试');
                    console.error(error);
                });
            },
            // 取消添加检验记录
            cancelAddInspection () {
                this.addInspectionModal = false;
            },
            // 打开文件选择器（检验记录）
            openFileInputInspection () {
                this.$refs.fileInputInspection.click();
            },
            // 处理文件上传（检验记录）
            handleFileChangeInspection (e) {
                const file = e.target.files[0];
                if (!file) return;

                this.uploadFileInspection(file);
            },
            async uploadFileInspection (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    if (response.data) {
                        this.inspectionForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        });
                        this.$refs.fileInputInspection.value = ''; // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data);
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试');
                    console.error(error);
                }
            },
            // 删除附件（检验记录）
            removeInspectionForm (index) {
                this.inspectionForm.attachments.splice(index, 1);
                this.$refs.fileInputInspection.value = ''; // 清空文件输入框的值
            },
            /**
             * 点击测评记录
             */
            viewAssessmentDetailsss (row) {
                this.viewAssessmentModal = true;
                this.AssessmentDetails(row.id);

            },
            /**
             * 测评记录详情方法
             */
            AssessmentDetails (id) {
                getAssessmentDetailsApi(id).then(res => {
                    if (res.status === 200) {
                        const data = res.data;
                        this.viewAssessmentForm = {
                            id: data.id,
                            type_text: data.type_text,
                            evaluation_time: data.evaluation_time,
                            evaluation_project: data.evaluation_project,
                            evaluation_result: data.evaluation_result,
                            attachments: data.files ? data.files.map(item => ({
                                url: item.att_dir,
                                name: item.filename,
                                att_id: item.att_id
                            })) : []
                        };
                        // 确保测评项目列表已加载
                        if (!this.assessmentProjects.length) {
                            this.getAssessmentProjects();
                        }
                    }
                }).catch(err => {
                    this.$Message.error('获取测评记录详情失败');
                });
            },
            /**
             * 点击测评记录编辑按钮
             */
            editAssessment (row) {
                this.editAssessmentModal = true;
                this.getAssessmentDetails(row.id);
            },
            // 获取测评记录详情
            getAssessmentDetails (id) {
                getAssessmentDetailsApi(id).then(res => {
                    if (res.status === 200) {
                        const data = res.data;
                        this.editAssessmentForm = {
                            id: data.id,
                            type: String(data.type),
                            evaluation_time: new Date(data.evaluation_time),
                            evaluation_project: data.evaluation_project,
                            evaluation_result: data.evaluation_result,
                            attachments: data.files ? data.files.map(item => ({
                                url: item.att_dir,
                                name: item.filename,
                                att_id: item.att_id
                            })) : []
                        };
                        // 确保测评项目列表已加载
                        if (!this.assessmentProjects.length) {
                            this.getAssessmentProjects();
                        }
                    }
                }).catch(err => {
                    this.$Message.error('获取测评记录详情失败');
                });
            },
            // 更新测评记录
            updateAssessment () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                const formattedDate = this.formatDate(this.editAssessmentForm.evaluation_time);
                // 这里添加提交数据的逻辑
                const formData = {
                    id: this.editAssessmentForm.id,
                    card_no: this.pushcard_no,
                    type: this.editAssessmentForm.type,
                    evaluation_time: formattedDate,
                    evaluation_project: this.editAssessmentForm.evaluation_project,
                    evaluation_result: this.editAssessmentForm.evaluation_result,
                    files: this.editAssessmentForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                };
                updateAssessmentApi(formData).then(res => {
                    if (res.status === 200) {
                        this.$Message.success(res.msg);
                        this.editAssessmentModal = false;
                        this.getevaluationrecordList(this.pushcard_no);
                    } else {
                        this.$Message.error('更新失败：' + res.msg);
                    }
                }).catch(err => {
                    this.$Message.error('更新出错，请稍后再试');
                    console.error(err);
                });
            },
            // 打开文件选择器（编辑测评记录）
            openFileInputEditAssessment () {
                this.$refs.fileInputEditAssessment.click();
            },
            // 处理文件上传（编辑测评记录）
            handleFileChangeEditAssessment (e) {
                const file = e.target.files[0];
                if (!file) return;

                this.uploadFileEditAssessment(file);
            },
            async uploadFileEditAssessment (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    if (response.data) {
                        this.editAssessmentForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        });
                        this.$refs.fileInputEditAssessment.value = ''; // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data);
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试');
                    console.error(error);
                }
            },
            // 删除附件（编辑测评记录）
            removeEditAssessmentAttachment (index) {
                this.editAssessmentForm.attachments.splice(index, 1);
            },
            // 取消编辑测评记录
            cancelEditAssessment () {
                this.editAssessmentModal = false;
            },
            openAddAssessmentModal () {
                this.addAssessmentModal = true;
                this.assessmentForm = {
                    type: '', // 类型
                    evaluation_time: '', // 测评时间
                    evaluation_project: '', // 测评项目
                    evaluation_result: '', // 测评结论
                    attachments: [] // 附件
                };
                this.getAssessmentProjects(); // 获取测评项目列表
            },
            // 获取测评项目列表
            getAssessmentProjects () {
                getAssessmentProjectsApi().then(res => {
                    if (res.status === 200) {
                        this.assessmentProjects = res.data;
                    }
                }).catch(err => {
                    this.$Message.error('获取测评项目列表失败');
                });
            },
            // 确认添加测评记录
            confirmAddAssessment () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                const formattedDate = this.formatDate(this.assessmentForm.evaluation_time);
                // 这里添加提交数据的逻辑
                const formData = {
                    card_no: this.pushcard_no,
                    type: this.assessmentForm.type,
                    evaluation_time: formattedDate,
                    evaluation_project: this.assessmentForm.evaluation_project,
                    evaluation_result: this.assessmentForm.evaluation_result,
                    files: this.assessmentForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                };
                console.log(formData, 'luoxin1')
                addAssessmentApi(formData).then(res => {
                    if (res.status === 200) {
                        this.$Message.success(res.msg);
                        this.addAssessmentModal = false;
                        this.getevaluationrecordList(this.pushcard_no);
                    }
                }).catch(error => {
                    this.$Message.error('添加失败，请稍后再试');
                    console.error(error);
                });
            },
            // 取消添加测评记录
            cancelAddAssessment () {
                this.addAssessmentModal = false;
            },
            // 打开文件选择器（测评记录）
            openFileInputAssessment () {
                this.$refs.fileInputAssessment.click();
            },
            // 处理文件上传（测评记录）
            handleFileChangeAssessment (e) {
                const file = e.target.files[0];
                if (!file) return;

                this.uploadFileAssessment(file);
            },
            async uploadFileAssessment (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    if (response.data) {
                        this.assessmentForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        });
                        this.$refs.fileInputAssessment.value = ''; // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data);
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试');
                    console.error(error);
                }
            },
            // 删除附件（测评记录）
            removeassessmentForm (index) {
                this.assessmentForm.attachments.splice(index, 1);
                this.$refs.fileInputAssessment.value = ''; // 清空文件输入框的值
            },
            // 获取咨询记录详情
            getMentalHealthDetailsss (row) {
                this.viewConsultationModal = true;
                const typeText = {
                    '0': '首次',
                    '1': '正式'
                }[row.type] || '未知类型'
                const consultation_type = {
                    '1': '现场咨询',
                    '2': '电话咨询',
                    '3': '视频咨询',
                    '4': '微信咨询'
                }[row.consultation_type] || '未知类型'
                // 调用接口获取咨询记录详情
                getConsultationDetailsApi(row.id).then(res => {
                    if (res.status == 200) {
                        console.log(res, '111111111111111111')
                        const data = res.data;
                        this.viewConsultationForm = {
                            id: data.id,
                            type: typeText,
                            type_text: data.type_text,
                            consultation_hours: data.consultation_hours,
                            consultation_lenth: data.consultation_lenth,
                            consultation_type: consultation_type,
                            visitor_description: data.visitor_description,
                            consultant_assessment: data.consultant_assessment,
                            suggestion: data.suggestion,
                            consultation_content: data.consultation_content,
                            attachments: data.files ? data.files.map(item => ({
                                url: item.att_dir,
                                name: item.filename,
                                att_id: item.att_id
                            })) : []
                        };
                    }
                }).catch(err => {
                    this.$Message.error('获取咨询记录详情失败');
                });
            },
            // 关闭查看咨询记录弹窗
            closeViewConsultation () {
                this.viewConsultationModal = false;
            },
            // 打开添加咨询记录弹窗
            openAddConsultationModal () {
                this.addConsultationModal = true;
                // 重置表单数据
                this.consultationForm = {
                    type: '0', // 默认初访
                    consultation_hours: '', // 咨询时间
                    consultation_lenth: '', // 咨询时长，默认60分钟
                    consultation_type: 1, // 默认现场咨询
                    visitor_description: '', // 来访者情况
                    consultant_assessment: '', // 咨询师评估
                    suggestion: '', // 建议/计划
                    consultation_content: '',
                    attachments: [] // 附件
                };
            },
            // 取消添加咨询记录
            cancelAddConsultation () {
                this.addConsultationModal = false;
            },
            // 确认添加咨询记录
            confirmAddConsultation () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                var formattedDate = this.formatDate(this.consultationForm.consultation_hours)
                // 这里添加提交数据的逻辑
                const formData = {
                    card_no: this.pushcard_no,
                    type: this.consultationForm.type,
                    consultation_hours: formattedDate,
                    consultation_lenth: this.consultationForm.consultation_lenth,
                    consultation_type: this.consultationForm.consultation_type,
                    visitor_description: this.consultationForm.visitor_description,
                    consultant_assessment: this.consultationForm.consultant_assessment,
                    suggestion: this.consultationForm.suggestion,
                    consultation_content: this.consultationForm.consultation_content,
                    files: this.consultationForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                }
                console.log(formData, '阔新')
                addconsultation(formData).then(res => {
                    if (res.status == 200) {
                        this.$Message.success(res.msg);
                        this.addConsultationModal = false
                        this.getconsultationList(this.pushcard_no)
                    }

                })
                // 这里添加将数据发送到后端的逻辑
                console.log('提交的咨询记录数据:', this.consultationForm);
                // 重置弹窗
                this.addConsultationModal = false;
            },
            // 打开文件选择器
            openFileInputConsultation () {
                this.$refs.fileInputConsultation.click();
            },
            // 处理文件上传
            handleFileChangeConsultation (e) {
                const file = e.target.files[0]
                if (!file) return

                this.uploadFileConsultation(file)
            },
            async uploadFileConsultation (file) {
                const formData = new FormData()
                formData.append('file', file)

                try {
                    // 替换为实际的文件上传接口
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    })
                    console.log(response.data, '99999')
                    if (response.data) {
                        this.consultationForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        })
                        console.log(this.consultationForm, 'da秘密')
                        this.$refs.fileInputConsultation.value = '' // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data)
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试')
                    console.error(error)
                }
            },
            /**
            * 心理咨询记录的新增弹窗里面的删除附件
            */
            removeconsultationForm (index) {
                this.consultationForm.attachments.splice(index, 1)
                this.$refs.fileInputConsultation.value = '' // 清空文件输入框的值
            },
            /**
             * 心理咨询记录的编辑弹窗的删除附近
             */
            removeEditConsultationForm (index) {
                this.editConsultationForm.attachments.splice(index, 1)
                // 清空文件输入框的值
                this.$refs.fileInputEditConsultation.value = ''
            },
            // 编辑跟进记录
            editFollowUp (row) {
                console.log(row, '888')
                this.editFollowUpModal = true
                this.editFollowUpForm = {
                    id: row.id,
                    type: String(row.type),
                    followTime: new Date(row.follow_time),
                    followContent: row.follow_content,
                    attachments: row.attachments ? row.attachments.map(item => ({
                        url: item.att_dir,
                        name: item.filename,
                        att_id: item.att_id
                    })) : []
                }
            },
            // 更新跟进记录
            updateFollowUp () {
                const formattedDate = this.formatDate(this.editFollowUpForm.followTime)
                const formData = {
                    id: this.editFollowUpForm.id,
                    card_no: this.pushcard_no,
                    type: this.editFollowUpForm.type,
                    follow_time: formattedDate,
                    follow_content: this.editFollowUpForm.followContent,
                    files: this.editFollowUpForm.attachments.map(item => item.att_id).join(',')
                }
                updatefollow(formData).then(res => {
                    if (res.status == 200) {
                        this.$Message.success(res.msg)
                        this.editFollowUpModal = false
                        this.getfollowList(this.pushcard_no)
                    } else {
                        this.$Message.error('更新失败：' + res.msg)
                    }
                }).catch(error => {
                    this.$Message.error('更新出错，请稍后再试')
                    console.error(error)
                })
            },
            // 取消编辑
            cancelEditFollowUp () {
                this.editFollowUpModal = false
            },
            openAddFollowUpModal () {
                this.addFollowUpModal = true
                this.followUpForm = {
                    type: '',
                    followTime: '',
                    followContent: '',
                    attachments: []
                }
            },
            cancelAddFollowUp () {
                this.addFollowUpModal = false
            },
            confirmAddFollowUp () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                var formattedDate = this.formatDate(this.followUpForm.followTime)
                // 这里添加提交数据的逻辑
                const formData = {
                    card_no: this.pushcard_no,
                    type: this.followUpForm.type,
                    follow_time: formattedDate,
                    follow_content: this.followUpForm.followContent,
                    files: this.followUpForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                }
                console.log(formData, '阔新')
                addfollow(formData).then(res => {
                    if (res.status == 200) {
                        this.$Message.success(res.msg);
                        this.addFollowUpModal = false
                        this.getfollowList(this.pushcard_no)
                    }

                })

            },
            openFileInput () {
                this.$refs.fileInput.click()
            },
            handleFileChange (e) {
                const file = e.target.files[0]
                if (!file) return

                this.uploadFile(file)
            },
            async uploadFile (file) {
                const formData = new FormData()
                formData.append('file', file)

                try {
                    // 替换为实际的文件上传接口
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    })
                    console.log(response.data, '99999')
                    if (response.data) {
                        this.followUpForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        })
                        console.log(this.followUpForm, 'da秘密')
                        this.$refs.fileInput.value = '' // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data)
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试')
                    console.error(error)
                }
            },
            openFileInputedit () {
                this.$refs.fileInputedit.click()
            },
            handleFileChanges (e) {
                const file = e.target.files[0]
                if (!file) return

                this.uploadFiles(file)
            },
            async uploadFiles (file) {
                const formData = new FormData()
                formData.append('file', file)

                try {
                    // 替换为实际的文件上传接口
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    })
                    console.log(response.data, '99999')
                    if (response.data) {
                        this.editFollowUpForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        })
                        console.log(this.editFollowUpForm, 'da秘密')
                        this.$refs.fileInputedit.value = '' // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data)
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试')
                    console.error(error)
                }
            },
            /**
             * 跟进记录的新增弹窗里面的删除附件
             */
            removeAttachment (index) {
                this.followUpForm.attachments.splice(index, 1)
                this.$refs.fileInput.value = '' // 清空文件输入框的值
            },
            /**
             * 跟进记录的编辑弹窗的删除
             */
            removeeditFollowUpForm (index) {
                this.editFollowUpForm.attachments.splice(index, 1)
                // 清空文件输入框的值
                this.$refs.fileInputedit.value = ''
            },
            isImage (url) {
                return /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(url)
            },
            getFileName (url) {
                return url.split('/').pop()
            },
            // 添加一个方法用于格式化日期
            formatDate (date) {
                if (!date) return ''
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                const hours = String(date.getHours()).padStart(2, '0')
                const minutes = String(date.getMinutes()).padStart(2, '0')
                const seconds = String(date.getSeconds()).padStart(2, '0')
                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
            },
            /**
             * 客户信息列表
             */
            getpatientlogList (card_no) {
                patientlogList({ card_no: card_no }).then(res => {
                    console.log(res.data, 'luoxxx1')
                    this.patientlog = res.data
                })
            },
            /**
             * 客户信息
             */
            getpatientInfo (card_no) {
                patientInfo({ card_no: card_no }).then(res => {
                    console.log(res.data, 'luo222')
                    
                    this.patientInfoList = res.data
                    //联系人信息
                    this.contact_person = res.data.contact_person
                    //父亲信息
                    this.father_info = res.data.father_info
                    //母亲信息
                    this.mather_info = res.data.mather_info
                    // 配偶信息
                    this.spouse_info = res.data.spouse_info
                    //家庭信息
                    this.family_info = res.data.family_info

                    //其他信息-个人精神类疾病史
                    this.history_mental = res.data.history_mental

                    //  其他信息-家族精神类疾病史
                    this.family_history_mental = res.data.family_history_mental

                    //其他信息-既往咨询经历
                    this.history_consultation = res.data.history_consultation

                    //其他信息-过往重大负面生活事件
                    this.life_import = res.data.life_import


                    console.log(res.data.contact_person, '99999')
                })
            },
            /**
             * 挂号记录
             */
            getregisteredorderList (card_no) {
                registeredorderList({ card_no: card_no }).then(res => {
                    this.registrationData = res.data
                })
            },
            /**
             * 诊疗记录
             */
            getmedicalrecordList (card_no) {
                medicalrecordList({ card_no: card_no }).then(res => {
                    this.treatmentData = res.data
                })
            },
            /**
             * 检验记录
             */
            gettestingrecordList (card_no) {
                testingrecordList({ card_no: card_no }).then(res => {
                    console.log(res.data, '99999999')
                    if (res.data.length > 0) {
                        this.inspectionData = res.data

                    }

                })
            },
            /**
             * 获取测评记录
             */
            getevaluationrecordList (card_no) {
                evaluationrecordList({ card_no: card_no }).then(res => {
                    console.log(res.data, '99999999')
                    if (res.data.length > 0) {
                        this.assessmentData = res.data

                    }

                })
            },
            /**
             * 获取心理咨询记录
             */
            getconsultationList (card_no) {
                consultationList({ card_no: card_no }).then(res => {
                    console.log(res.data, '99999999')
                    if (res.data.length > 0) {
                        this.mentalHealthData = res.data

                    }

                })
            },
            /**
             * 获取跟进记录
             */
            getfollowList (card_no) {
                this.pushcard_no = card_no
                followList({ card_no: card_no }).then(res => {
                    if (res.data.length > 0) {
                        this.followUpData = res.data
                    } else {
                        this.followUpData = [] // 如果没有数据，清空数组
                    }
                }).catch(error => {
                    console.error('获取跟进记录失败:', error)
                    this.followUpData = [] // 获取失败时清空数组
                })
            },

            /**
             * 获取康讯记录
             */
            gettrainingList (card_no) {
                this.pushcard_no = card_no
                gethealthtrainingList({ card_no: card_no,page:1,limit:100 }).then(res => {
                    if (res.data.list.length > 0) {
                        this.trainingListData = res.data.list
                    } else {
                        this.trainingListData = [] // 如果没有数据，清空数组
                    }
                }).catch(error => {
                    console.error('获取跟进记录失败:', error)
                    this.trainingListData = [] // 获取失败时清空数组
                })
            },





            /**
             * 编辑就诊人薪资
             */
            showEditModal () {
                patientInfo({ card_no: this.pushcard_no }).then(res=>{
                    this.editFormData = res.data
                    //联系人信息
                    this.editFormData.contact_person =res.data.contact_person
                    this.editFormData.infoId = res.data.id
                    console.log(res,'洛溪大哥哥',this.editFormData.contact_person)
                     //父亲信息
                    this.editFormData.father_info = res.data.father_info
                    //母亲信息
                    this.editFormData.mather_info = res.data.mather_info
                    //配偶信息
                    this.editFormData.spouse_info = res.data.spouse_info

                     //家庭信息
                    this.editFormData.family_info = res.data.family_info

                    //其他信息-个人精神类疾病史
                    this.editFormData.history_mental = res.data.history_mental

                    //  其他信息-家族精神类疾病史
                    this.editFormData.family_history_mental = res.data.family_history_mental

                    //其他信息-既往咨询经历
                    this.editFormData.history_consultation = res.data.history_consultation

                    //其他信息-过往重大负面生活事件
                    this.editFormData.life_import = res.data.life_import
                    let helpTopicArray = [];
                    if (res.data.help_topic) {
                        helpTopicArray = res.data.help_topic.split(',');
                    }
                    this.editFormData = {
                        ...res.data,
                        help_topic: helpTopicArray, // 确保 help_topic 是一个数组
                    };
                    this.editFormData.other_help_topic = res.data.other_help_topic || '';
                    this.isOtherChecked = res.data.help_topic.includes('其他');
                   
                })

                // 显示编辑模态框
                this.editModalVisible = true;
            },
            /**
             * 生日转换
             */
            formatBirthday(isoDate) {
                const date = new Date(isoDate);
                const year = date.getFullYear();
                let month = date.getMonth() + 1;
                let day = date.getDate();
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                return `${year}-${month}-${day}`;
            },
            saveEdit () {
                 const formattedDate = this.formatBirthday(this.editFormData.birthday);
                 const helpTopicString = this.editFormData.help_topic.join(',');
                 const formData = {
                    card_no: this.pushcard_no,
                    birthday: formattedDate,
                    marital_status: this.editFormData.marital_status,
                    home_address: this.editFormData.home_address,
                    special_marking: this.editFormData.special_marking,
                    remark: this.editFormData.remark,
                    //联系人信息
                    contact_person:this.editFormData.contact_person,
                    //父亲信息
                    father_info:this.editFormData.father_info,
                    //母亲信息
                    mather_info:this.editFormData.mather_info,
                    //配偶信息
                    spouse_info:this.editFormData.spouse_info,
                    //父母婚姻状态
                    family_info:this.editFormData.family_info,
                    //其他信息-
                    past_year:this.editFormData.past_year,
                    friend_situation:this.editFormData.friend_situation,
                    history_mental: {
                        option: this.editFormData.history_mental.option,
                        text: this.editFormData.history_mental.text
                    },
                    is_self_harm: this.editFormData.is_self_harm,
                    family_history_mental: {
                        option: this.editFormData.family_history_mental.option,
                        text: this.editFormData.family_history_mental.text
                    },
                    history_consultation: {
                        option: this.editFormData.history_consultation.option
                    },
                    life_import: {
                        option: this.editFormData.life_import.option,
                        text: this.editFormData.life_import.text
                    },
                    help_topic:helpTopicString,
                    other_help_topic:this.editFormData.other_help_topic,
                    id:this.editFormData.infoId
                };
                // 调用接口提交数据
                updatePatientInfo(formData).then(res => {
                    if (res.status === 200) {
                        this.$Message.success('编辑成功');
                        // 刷新页面数据
                        this.getpatientInfo(this.pushcard_no);
                    } else {
                        this.$Message.error('编辑失败: ' + res.msg);
                    }
                }).catch(err => {
                    this.$Message.error('编辑出错，请稍后再试');
                    console.error(err);
                });
                // 保存编辑
                console.log('保存编辑:', this.editFormData);
                this.editModalVisible = false;

            },
            cancelEdit () {
                // 取消编辑
                this.editModalVisible = false;
            },
            viewFollowUpDetails (row) {
                this.viewFollowUpModal = true
                const typeText = {
                    '0': '回访',
                    '1': '邀约'
                }[row.type] || '未知类型'
                this.viewFollowUpForm = {
                    id: row.id,
                    type: typeText,
                    followTime: row.follow_time,
                    followContent: row.follow_content,
                    attachments: row.attachments ? row.attachments.map(item => ({
                        url: item.att_dir,
                        name: item.filename,
                        att_id: item.att_id
                    })) : []
                }

                // 查看跟进记录详情的逻辑
                console.log('查看跟进记录详情:', row);
            },
            /**
             * 删除跟进记录
             */
            deleteFollowUp (row, tit, num) {
                let FollowUp = {
                    title: tit,
                    num: num,
                    url: `user/del_follow?id=${row.id}`,
                    method: "DELETE",
                };
                this.$modalSure(FollowUp).then((res) => {
                    this.$Message.success(res.msg);
                    this.followUpData = this.followUpData.filter(item => item.id !== row.id);
                    this.getfollowList(this.pushcard_no);
                }).catch((res) => {
                    this.$Message.error(res.msg);
                });
                // 删除跟进记录的逻辑
                console.log('删除跟进记录:', row);
            },
            editMentalHealth (row) {
                this.editConsultationModal = true;
                this.getMentalHealthDetails(row.id);
                // 编辑心理咨询记录的逻辑
                console.log('编辑心理咨询记录:', row);
            },
            // 获取咨询记录详情
            getMentalHealthDetails (id) {
                // 调用接口获取咨询记录详情
                getConsultationDetailsApi(id).then(res => {
                    if (res.status === 200) {
                        const data = res.data;
                        this.editConsultationForm = {
                            id: data.id,
                            type: String(data.type),
                            consultation_hours: new Date(data.consultation_hours),
                            consultation_lenth: data.consultation_lenth,
                            consultation_type: String(data.consultation_type),
                            visitor_description: data.visitor_description,
                            consultant_assessment: data.consultant_assessment,
                            suggestion: data.suggestion,
                            consultation_content: data.consultation_content || '',
                            attachments: data.files ? data.files.map(item => ({
                                url: item.att_dir,
                                name: item.filename,
                                att_id: item.att_id
                            })) : []
                        };
                    }
                }).catch(err => {
                    this.$Message.error('获取咨询记录详情失败');
                });
            },
            // 更新咨询记录
            updateConsultation () {
                // 将日期对象转换为 "yyyy-MM-dd HH:mm:ss" 格式
                const formattedDate = this.formatDate(this.editConsultationForm.consultation_hours);
                // 这里添加提交数据的逻辑
                const formData = {
                    id: this.editConsultationForm.id,
                    card_no: this.pushcard_no,
                    type: this.editConsultationForm.type,
                    consultation_hours: formattedDate,
                    consultation_lenth: this.editConsultationForm.consultation_lenth,
                    consultation_type: this.editConsultationForm.consultation_type,
                    visitor_description: this.editConsultationForm.visitor_description,
                    consultant_assessment: this.editConsultationForm.consultant_assessment,
                    suggestion: this.editConsultationForm.suggestion,
                    consultation_content: this.editConsultationForm.consultation_content, // 包含咨询内容
                    files: this.editConsultationForm.attachments.map(item => item.att_id).join(',') // 将附件URL用逗号分隔
                };
                updateConsultationApi(formData).then(res => {
                    if (res.status === 200) {
                        this.$Message.success(res.msg);
                        this.editConsultationModal = false;
                        this.getconsultationList(this.pushcard_no);
                    } else {
                        this.$Message.error('更新失败：' + res.msg);
                    }
                }).catch(err => {
                    this.$Message.error('更新出错，请稍后再试');
                    console.error(err);
                });
            },
            // 打开文件选择器（编辑咨询记录）
            openFileInputEditConsultation () {
                this.$refs.fileInputEditConsultation.click();
            },
            // 处理文件上传（编辑咨询记录）
            handleFileChangeEditConsultation (e) {
                const file = e.target.files[0];
                if (!file) return;

                this.uploadFileEditConsultation(file);
            },
            async uploadFileEditConsultation (file) {
                const formData = new FormData();
                formData.append('file', file);

                try {
                    const response = await axios.post('https://67686161.com/adminapi/file/attachment/upload', formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data',
                            'Authori-zation': 'Bearer ' + util.cookies.get('token')
                        }
                    });
                    if (response.data) {
                        this.editConsultationForm.attachments.push({
                            url: response.data.data.url,
                            name: response.data.data.filename,
                            att_id: response.data.data.att_id
                        });
                        this.$refs.fileInputEditConsultation.value = ''; // 清空文件输入框的值
                    } else {
                        this.$Message.error('上传失败：' + response.data);
                    }
                } catch (error) {
                    this.$Message.error('上传出错，请稍后再试');
                    console.error(error);
                }
            },
            // 删除附件（编辑咨询记录）
            removeEditAttachment (index) {
                this.editConsultationForm.attachments.splice(index, 1);
            },
            // 取消编辑咨询记录
            cancelEditConsultation () {
                this.editConsultationModal = false;
            },

            /**
             * 删除心理咨询
             */
            deleteMentalHealth (row, tit, num) {
                console.log(row, '999')
                let MentalHealth = {
                    title: tit,
                    num: num,
                    url: `user/del_consultation?id=${row.id}`,
                    method: "DELETE",
                };
                console.log(MentalHealth, '777')
                this.$modalSure(MentalHealth).then((res) => {
                    this.$Message.success(res.msg);
                    this.mentalHealthData = this.mentalHealthData.filter(item => item.id !== row.id);
                    this.getconsultationList(this.pushcard_no);
                }).catch((res) => {
                    this.$Message.error(res.msg);
                });

            },
            /**
             * 删除测评记录
             */
            deleteAssessment (row, tit, num) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `user/del_evaluation_record?id=${row.id}`,
                    method: "DELETE",
                    ids: "",
                };
                this.$modalSure(delfromData)
                    .then((res) => {
                        this.$Message.success(res.msg);
                        this.assessmentData = this.assessmentData.filter(item => item.id !== row.id);
                        this.getevaluationrecordList(this.pushcard_no);
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });

                // 删除测评记录的逻辑
                console.log('删除测评记录:', row)
            },
            viewInspectionDetailsss (row) {
                this.viewInspectionModal = true;
                this.getviewInspection(row.id);
            },
            getviewInspection(id) {
                getInspectionDetailsApi(id).then(res => {
                if (res.status === 200) {
                    const data = res.data;
                    this.viewInspectionForm = {
                        id: data.id,
                        type:data.type,
                        type_text:data.type_text,
                        testing_time:data.testing_time,
                        testing_project: data.testing_project,
                        testing_result: data.testing_result,
                        attachments: data.files ? data.files.map(item => ({
                            url: item.att_dir,
                            name: item.filename,
                            att_id: item.att_id
                        })) : []
                    };
                    // 确保检验项目列表已加载
                    if (!this.inspectionProjects.length) {
                        this.getInspectionProjects();
                    }
                }
            }).catch(err => {
                this.$Message.error('获取检验记录详情失败');
            });
            },
            /**
             * 删除检验记录
             */
            deleteInspection (row, tit, num) {
                let delfromData = {
                    title: tit,
                    num: num,
                    url: `user/del_testing_record?id=${row.id}`,
                    method: "DELETE",
                    ids: "",
                };
                this.$modalSure(delfromData)
                    .then((res) => {
                        this.$Message.success(res.msg);
                        this.inspectionData = this.inspectionData.filter(item => item.id !== row.id);
                        this.gettestingrecordList(this.pushcard_no);
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            viewTreatmentDetails (row) {
                // 查看诊疗记录详情的逻辑
                this.treatmentDetailVisible = true;
                this.treatmentDetail = row.extension_detail
                console.log('查看诊疗记录详情:', row)
            },
            resetTreatmentDetail () {
                this.treatmentDetailVisible = false;
                // 重置诊疗详情数据
                this.treatmentDetail = {};
            },
            allStore () {
                staffListInfo().then(res => {
                    this.storeList = res.data;
                }).catch(err => {
                    this.$Message.error(err.msg);
                })
            },
            checkboxItem (e) {
                let uid = parseInt(e.rowid);
                let index = this.checkUidList.indexOf(uid);
                if (index !== -1) {
                    this.checkUidList = this.checkUidList.filter((item) => item !== uid);
                } else {
                    this.checkUidList.push(uid);
                }
            },
            checkboxAll () {
                // 获取选中当前值
                let obj2 = this.$refs.xTable.getCheckboxRecords(true);
                // 获取之前选中值
                let obj = this.$refs.xTable.getCheckboxReserveRecords(true);
                if (this.isAll == 0 && this.checkUidList.length <= obj.length && !this.isCheckBox) {
                    obj = [];
                }
                obj = obj.concat(obj2);
                let uids = [];
                obj.forEach((item) => {
                    uids.push(parseInt(item.uid))
                })
                this.checkUidList = uids;
                if (!obj2.length) {
                    this.isCheckBox = false;
                }
            },
            allPages (e) {
                this.isAll = e;
                if (e == 0) {
                    this.$refs.xTable.toggleAllCheckboxRow();
                    // this.checkboxAll();
                } else {
                    if (!this.isCheckBox) {
                        this.$refs.xTable.setAllCheckboxRow(true);
                        this.isCheckBox = true;
                        this.isAll = 1;
                    } else {
                        this.$refs.xTable.setAllCheckboxRow(false);
                        this.isCheckBox = false;
                        this.isAll = 0;
                    }
                    this.checkUidList = []
                }
            },
            closeLabel (label) {
                let index = this.dataLabel.indexOf(
                    this.dataLabel.filter((d) => d.id == label.id)[0]
                );
                this.dataLabel.splice(index, 1);
            },
            activeData (dataLabel) {
                this.labelListShow = false;
                if (this.batchModal && this.menuActive === 2) {
                    this.batchLabel = dataLabel;
                    this.batchData.label_id = dataLabel.map(item => item.id);
                } else {
                    this.dataLabel = dataLabel;
                }
            },
            openLabelList (row) {
                this.labelListShow = true;
                let data = JSON.parse(JSON.stringify(this.dataLabel));
                if (this.batchModal && this.menuActive === 2) {
                    data = JSON.parse(JSON.stringify(this.batchLabel));
                }
                this.$refs.labelList.userLabel(data);
            },
            // 标签弹窗关闭
            labelListClose () {
                this.labelListShow = false;
            },
            // 标签弹窗关闭
            labelClose (e) {
                if (!e) {
                    this.getList();
                }
                this.labelShow = false;
                this.labelActive.uid = 0;
            },
            // 提交
            putSend (name) {
                this.$refs[name].validate((valid) => {
                    if (valid) {
                        if (!this.formInline.spread_uid) {
                            return this.$Message.error("请上传用户");
                        }
                        agentSpreadApi(this.formInline)
                            .then((res) => {
                                this.promoterShow = false;
                                this.$Message.success(res.msg);
                                this.getList();
                                this.$refs[name].resetFields();
                            })
                            .catch((res) => {
                                this.$Message.error(res.msg);
                            });
                    }
                });
            },
            save () {
                this.$modalForm(getUserSaveForm()).then(() => this.getList());
            },
            synchro () {
                userSynchro()
                    .then((res) => {
                        this.$Message.success(res.msg);
                    })
                    .catch((err) => {
                        this.$Message.error(err.msg);
                    });
            },
            // 分组列表
            groupLists () {
                this.loading = true;
                userLabelApi(this.labelFrom)
                    .then(async (res) => {
                        let data = res.data;
                        this.labelLists = data.list;
                    })
                    .catch((res) => {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    });
            },
            onClickTab (type) {
                this.isAll = 0;
                this.isCheckBox = false;
                this.$refs.xTable.setAllCheckboxRow(false);
                this.checkUidList = [];
                this.userFrom.page = 1;
                this.userFrom.user_type = type == -1 ? '' : type;
                this.getList();
            },
            userGroup () {
                let data = {
                    page: 1,
                    limit: "",
                };
                userGroupApi(data).then((res) => {
                    this.groupList = res.data.list;
                });
            },
            levelLists () {
                let data = {
                    page: 1,
                    limit: "",
                    title: "",
                    is_show: 1,
                };
                levelListApi(data).then((res) => {
                    this.levelList = res.data.list;
                });
            },
            // 批量设置分组；
            setGroup () {
                if (this.selectionList.length === 0) {
                    this.$Message.warning("请选择要设置分组的用户");
                } else {
                    let uids = {
                        all: this.isAll,
                        uids: this.array_ids
                    };
                    if (this.isAll == 1) {
                        uids.where = this.userFrom;
                        uids.where = {
                            city: this.userFrom.city,
                            country: this.userFrom.country,
                            field_key: this.userFrom.field_key,
                            group_id: this.userFrom.group_id,
                            isMember: this.userFrom.isMember,
                            is_promoter: this.userFrom.is_promoter,
                            label_id: this.userFrom.label_id,
                            level: this.userFrom.level,
                            nickname: this.userFrom.nickname,
                            pay_count: this.userFrom.pay_count,
                            province: this.userFrom.province,
                            sex: this.userFrom.sex,
                            status: this.userFrom.status,
                            user_time: this.userFrom.user_time,
                            user_time_type: this.userFrom.user_time_type,
                            user_type: this.userFrom.user_type,
                        };
                    }
                    this.$modalForm(userSetGroup(uids)).then(() => this.getList());
                }
            },
            // 批量设置标签；
            setLabel () {
                if (this.selectionList.length === 0) {
                    this.$Message.warning("请选择要设置标签的用户");
                } else {
                    let uids = {
                        all: this.isAll,
                        uids: this.array_ids
                    };
                    if (this.isAll == 1) {
                        uids.where = {
                            city: this.userFrom.city,
                            country: this.userFrom.country,
                            field_key: this.userFrom.field_key,
                            group_id: this.userFrom.group_id,
                            isMember: this.userFrom.isMember,
                            is_promoter: this.userFrom.is_promoter,
                            label_id: this.userFrom.label_id,
                            level: this.userFrom.level,
                            nickname: this.userFrom.nickname,
                            pay_count: this.userFrom.pay_count,
                            province: this.userFrom.province,
                            sex: this.userFrom.sex,
                            status: this.userFrom.status,
                            user_time: this.userFrom.user_time,
                            user_time_type: this.userFrom.user_time_type,
                            user_type: this.userFrom.user_type,
                        };
                    }
                    this.labelShow = true;
                    this.labelActive.uid = uids;
                }
            },
            // 是否为付费会员；
            changeMember () {
                this.userFrom.page = 1;
                this.getList();
            },
            // 选择国家
            changeCountry () {
                if (this.userFrom.country === "abroad" || !this.userFrom.country) {
                    this.selectedData = [];
                    this.userFrom.province = "";
                    this.userFrom.city = "";
                    this.address = [];
                }
            },
            // 选择地址
            handleChange (value, selectedData) {
                this.selectedData = selectedData.map((o) => o.label);
                this.userFrom.province = this.selectedData[0];
                this.userFrom.city = this.selectedData[1];
            },
            // 具体日期
            onchangeTime (e) {
                this.timeVal = e;
                this.userFrom.user_time = this.timeVal[0] ? this.timeVal.join("-") : "";
            },
            // 操作
            changeMenu (row, name, index) {
                this.userId = row.bind_uid;
                let uid = [];
                uid.push(row.bind_uid);
                let uids = { uids: uid };
                switch (name) {
                    case "1":
                        this.$refs.userDetails.modals = true;
                        this.$refs.userDetails.activeName = "info";
                        this.$refs.userDetails.getDetails(row.bind_uid);
                        break;
                    case "2":
                        this.getOtherFrom(row.uid);
                        break;
                    case "3":
                        // this.giveLevel(row.uid);
                        this.giveLevelTime(row.uid);
                        break;
                    case "4":
                        this.del(
                            row,
                            "清除 【 " + row.nickname + " 】的会员等级",
                            index,
                            "user"
                        );
                        break;
                    case "5":
                        this.$modalForm(userSetGroup(uids)).then(() =>
                            this.$refs.sends.getList()
                        );
                        break;
                    case "6":
                        this.openLabel(row);
                        break;
                    case "7":
                        this.editS(row);
                        break;
                    case "8":
                        // 设置抽屉标题所需的患者信息
                        this.currentPatientName = row.patient_name; // 假设就诊人名称在row.nickname字段
                        this.currentPatientAge = row.phone;       // 假设就诊人年龄在row.age字段
                        this.drawerVisible = true;
                        this.getpatientlogList(row.card_no)
                        this.getpatientInfo(row.card_no)
                        //获取挂号记录
                        this.getregisteredorderList(row.card_no)
                        //获取诊疗记录
                        this.getmedicalrecordList(row.card_no)
                        //获取检验记录
                        this.gettestingrecordList(row.card_no)
                        //获取测评记录
                        this.getevaluationrecordList(row.card_no)
                        //获取心理咨询记录
                        this.getconsultationList(row.card_no)
                        //获取跟进记录
                        this.getfollowList(row.card_no)
                        //获取康讯记录
                        this.gettrainingList(row.card_no)
                        break;
                    default:
                        this.del(
                            row,
                            "解除【 " + row.nickname + " 】的上级推广人",
                            index,
                            "tuiguang"
                        );
                        break;
                }
            },
            openLabel (row) {
                this.labelShow = true;
                this.labelActive.uid = row.uid;
            },
            editS (row) {
                this.promoterShow = true;
                this.formInline.uid = row.uid;
            },
            customer () {
                this.customerShow = true;
            },
            imageObject (e) {
                this.customerShow = false;
                if (this.batchModal && this.menuActive === 6) {
                    this.batchData.spread_uid = e.uid;
                    this.spread_name = e.name;
                } else {
                    this.formInline.spread_uid = e.uid;
                    this.formInline.image = e.image;
                }
            },
            cancel (name) {
                this.promoterShow = false;
                this.$refs[name].resetFields();
            },
            // 赠送会员等级
            giveLevel (id) {
                giveLevelApi(id)
                    .then(async (res) => {
                        if (res.data.status === false) {
                            return this.$authLapse(res.data);
                        }
                        this.FromData = res.data;
                        this.$refs.edits.modals = true;
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            // 赠送会员等级
            giveLevelTime (id) {
                giveLevelTimeApi(id)
                    .then(async (res) => {
                        if (res.data.status === false) {
                            return this.$authLapse(res.data);
                        }
                        this.FromData = res.data;
                        this.$refs.edits.modals = true;
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },

            // 清除会员删除成功
            submitModel () {
                this.getList();
            },
            // 会员列表
            getList () {
                this.loading = true;
                let activeIds = [];
                this.dataLabel.forEach((item) => {
                    activeIds.push(item.id);
                });
                this.userFrom.label_id = activeIds.join(",") || "";
                this.userFrom.user_type = this.userFrom.user_type || "";
                this.userFrom.status = this.userFrom.status || "";
                this.userFrom.sex = this.userFrom.sex || "";
                this.userFrom.is_promoter = this.userFrom.is_promoter || "";
                this.userFrom.country = this.userFrom.country || "";
                this.userFrom.user_time_type = this.userFrom.user_time_type || "";
                this.userFrom.pay_count = this.userFrom.pay_count || "";
                // this.userFrom.label_id = this.userFrom.label_id || "";
                this.userFrom.field_key = this.field_key === "all" ? "" : this.field_key;

                 this.userFrom.card_no = this.userFrom.card_no || "";
                 this.userFrom.id_card = this.userFrom.id_card || "";
                 this.userFrom.phone_number = this.userFrom.phone_number || "";
                 this.userFrom.patient_name = this.userFrom.patient_name || "";

                this.userFrom.level =
                    this.userFrom.level === "all" ? "" : this.userFrom.level;
                this.userFrom.group_id =
                    this.userFrom.group_id === "all" ? "" : this.userFrom.group_id;
                patientList(this.userFrom)
                    .then(async (res) => {
                        let data = res.data;
                        data.list.forEach((item) => {
                            item.checkBox = false;
                        });
                        console.log(data.list, '罗鑫')
                        this.userLists = data.list;
                        this.total = data.count;
                        this.loading = false;
                        this.$nextTick(function () {
                            if (this.isAll == 1) {
                                this.selectionList = this.userLists;
                                if (this.isCheckBox) {
                                    this.$refs.xTable.setAllCheckboxRow(true);
                                } else {
                                    this.$refs.xTable.setAllCheckboxRow(false);
                                }
                            } else {
                                let obj = this.$refs.xTable.getCheckboxReserveRecords(true);
                                if (!this.checkUidList.length || this.checkUidList.length <= obj.length) {
                                    this.$refs.xTable.setAllCheckboxRow(false);
                                }
                            }
                        })

                    })
                    .catch((res) => {
                        this.loading = false;
                        this.$Message.error(res.msg);
                    });
            },
            pageChange ({ currentPage, pageSize }) {
                this.userFrom.page = currentPage;
                this.userFrom.limit = pageSize;
                this.getList();
            },
            // 搜索
            userSearchs () {
                if (this.userFrom.user_time_type && !this.timeVal.length) {
                    return this.$Message.error("请选择访问时间");
                }
                if (this.timeVal.length && !this.userFrom.user_time_type) {
                    return this.$Message.error("请选择访问情况");
                }
                this.isAll = 0;
                this.$refs.xTable.setAllCheckboxRow(false);
                this.checkUidList = [];
                this.userFrom.page = 1;
                this.selectionList = [];
                this.getList();
            },
            // 重置
            reset (name) {
                this.$refs.xTable.setAllCheckboxRow(false);
                this.checkUidList = []
                this.headeType = "-1";
                this.userFrom = {
                    user_type: "",
                    status: "",
                    sex: "",
                    is_promoter: "",
                    country: "",
                    pay_count: "",
                    user_time_type: "",
                    user_time: "",
                    nickname: "",
                    field_key: "",
                    level: "",
                    group_id: "",
                    label_id: "",
                    card_no:"",
                    id_card:"",
                    phone_number:"",
                    patient_name:"",
                    page: 1, // 当前页
                    limit: 20, // 每页显示条数
                };
                this.field_key = "";
                this.level = "";
                this.group_id = "";
                this.label_id = "";
                this.user_time_type = "";
                this.pay_count = "";
                this.timeVal = [];
                this.selectionList = [];
                this.dataLabel = [];
                this.getList();
                this.registrationData = []
                this.treatmentData = []
                this.inspectionData = [] 
                this.assessmentData  = [] 
                this.mentalHealthData   = [] 
                this.followUpData = [] 
                this.trainingListData = []

            },
            // 获取编辑表单数据
            getUserFrom (id) {
                this.$modalForm(getUserData(id)).then(() => this.getList());
            },
            // 获取积分余额表单
            getOtherFrom (id) {
                editOtherApi(id)
                    .then(async (res) => {
                        if (res.data.status === false) {
                            return this.$authLapse(res.data);
                        }
                        res.data.rules[1].props.max = 999999;
                        this.FromData = res.data;
                        this.$refs.edits.modals = true;
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            // 修改状态
            onchangeIsShow (row) {
                let data = {
                    id: row.uid,
                    status: row.status,
                };
                isShowApi(data)
                    .then(async (res) => {
                        this.$Message.success(res.msg);
                    })
                    .catch((res) => {
                        this.$Message.error(res.msg);
                    });
            },
            // 点击发送优惠券
            onSend () {
                if (this.checkUidList.length === 0 && this.isAll == 0) {
                    return this.$Message.warning("请选择要发送优惠券的用户");
                }
                this.$refs.sends.modals = true;
                this.$refs.sends.getList();
            },
            // 发送图文消息
            onSendPic () {
                if (this.checkUidList.length === 0 && this.isAll == 0) {
                    this.$Message.warning("请选择要发送图文消息的用户");
                } else {
                    this.modal13 = true;
                }
            },
            // 编辑
            edit (row) {
                this.getUserFrom(row.uid);
            },
            //信息补充
            extendInfo (row) {
                extendInfo(row.uid).then(async (res) => {
                    if (res.data.status === false) {
                        return this.$authLapse(res.data);
                    }
                    this.FromData = res.data;
                    this.$refs.edits.modals = true;
                    // this.getList()
                }).catch(err => {
                    this.$Message.error(err.msg);
                })
            },
            // 修改成功
            submitFail (p) {
                if (this.$refs.userDetails.modals) {
                    this.$refs.userDetails.getDetails(this.userId);
                }
            },
            menuSelect (name) {
                this.menuActive = name;
            },
            setBatch () {
                this.batchModal = true;
            },
            tagClose (id) {
                let index = this.batchLabel.findIndex(item => item.id === id);
                this.batchLabel.splice(index, 1);
            },
            cancelBatch () {
                this.batchModal = false;
            },
            // 保存批量操作
            saveBatch () {
                batchProcess({
                    type: this.menuActive,
                    uids: this.checkUidList,
                    all: this.isAll,
                    where: this.userFrom,
                    data: this.batchData
                }).then(res => {
                    this.$Message.success(res.msg);
                    this.batchModal = false;
                }).catch(res => {
                    this.$Message.error(res.msg);
                });
            },
            batchVisibleChange () {
                this.batchData = {
                    group_id: 0,
                    label_id: [],
                    level_id: 0,
                    money_status: 0,
                    money: 0,
                    integration_status: 0,
                    integration: 0,
                    days_status: 1,
                    day: 0,
                    spread_uid: '',
                };
                this.batchLabel = [];
                this.spread_name = '';
                this.menuActive = 1;
            }
        },
    };
</script>

<style scoped lang="stylus">
    /deep/.ivu-dropdown-item
        font-size 12px !important
    /deep/.vxe-table--render-default .vxe-cell
        font-size 12px
    .expand-row
        margin-bottom 16px
        font-size 12px
    .tdinfo
        margin-left 88px
        margin-top 15px
    .padding-add
        padding 20px 20px 0
    .input-add
        max-width 250px
    .labelInput
        max-width 250px
        border 1px solid #dcdee2
        padding 0 5px
        border-radius 5px
        min-height 30px
        cursor pointer
        .span
            color #c5c8ce
        .iconxiayi
            font-size 12px
    .picBox
        display inline-block
        cursor pointer
        .upLoad
            width 58px
            height 58px
            line-height 58px
            border 1px dotted rgba(0, 0, 0, 0.1)
            border-radius 4px
            background rgba(0, 0, 0, 0.02)
        .pictrue
            width 60px
            height 60px
            border 1px dotted rgba(0, 0, 0, 0.1)
            margin-right 10px
            img
                width 100%
                height 100%
    .userFrom
        >>> .ivu-form-item-content
            margin-left 0px !important
    .userAlert
        margin-top 20px
    .userI
        color #1890FF
        font-style normal
    img
        height 36px
        display block
    .tabBox_img
        width 36px
        height 36px
        border-radius 4px
        cursor pointer
        img
            width 100%
            height 100%
    .tabBox_tit
        width 60%
        font-size 12px !important
        margin 0 2px 0 10px
        letter-spacing 1px
        padding 5px 0
        box-sizing border-box
    .modelBox
        >>> .ivu-modal-body
            padding 0 16px 16px 16px !important
    .vipName
        color #dab176
    .listbox
        >>>.ivu-divider-horizontal
            margin 0 !important
    /deep/.ivu-table-header
        // overflow visible
    /deep/.ivu-table th
        overflow visible
    /deep/.select-item:hover
        background-color #f3f3f3
    /deep/.select-on
        display block
    /deep/.select-item.on
        /* background: #f3f3f3; */
    .pane_pd
        padding 4px 16px 20px !important
        font-weight 500
    .new_tab
        >>>.ivu-tabs-nav .ivu-tabs-tab
            padding 4px 16px 20px !important
            font-weight 500
    .dateMedia
        /deep/.ivu-form-item-content
            max-width 250px
            /deep/.ivu-date-picker
                width 100%
    .select-tag
        position relative
        min-height 32px
        padding 0 24px 0 4px
        border 1px solid #dcdee2
        border-radius 4px
        line-height normal
        user-select none
        cursor pointer
        &:hover
            border-color #57a3f3
        .ivu-icon
            position absolute
            top 50%
            right 8px
            line-height 1
            transform translateY(-50%)
            font-size 14px
            color #808695
            transition all 0.2s ease-in-out
        .ivu-tag
            position relative
            max-width 99%
            height 24px
            margin 3px 4px 3px 0
            line-height 22px
        .placeholder
            display block
            height 30px
            line-height 30px
            color #c5c8ce
            font-size 12px
            overflow hidden
            text-overflow ellipsis
            white-space nowrap
            padding-left 4px
            padding-right 22px
    >>> .batch-modal
        .ivu-modal-body
            padding 0
        .ivu-alert
            margin 12px 24px
        .ivu-col-span-4
            flex none
            width 130px
        .ivu-col-span-20
            padding-right 37px
        .ivu-input-number
            width 100%
        .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu)
            z-index auto
        .ivu-menu-light.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):after
            right auto
            left 0
        .ivu-menu-item
            padding-right 0
</style>
<style scoped lang="stylus">
    .drawer-content
        height 100%
        overflow auto
    .info-content
        width 100%
    .info-header
        margin-bottom 20px
    .record-item
        margin-bottom 20px
    .record-date
        font-weight bold
        margin-bottom 10px
    .record-item-content
        margin-bottom 10px
        display flex
    .record-time
        width 80px
        font-weight bold
    .record-content
        flex 1
    .record-title
        font-weight bold
        margin-bottom 5px
    .record-info
        display flex
        flex-wrap wrap
    .record-info-item
        margin-right 20px
        margin-bottom 5px
</style>

<style scoped lang="stylus">
    .customer-info
        padding 20px
    .info-section
        margin-bottom 30px
    .info-section h3
        font-size 16px
        font-weight bold
        margin-bottom 15px
        padding-bottom 10px
        border-bottom 1px solid #eee
    .info-item
        margin-bottom 12px
    .info-label
        font-weight bold
        margin-right 5px
    .info-content
        color #666
</style>
<style scoped lang="stylus">
    .registration-records
        padding 20px
</style>
<style scoped lang="stylus">
    .treatment-records
        padding 20px
</style>
<style scoped lang="stylus">
    .inspection-records
        padding 20px
</style>
<style scoped lang="stylus">
    .assessment-records
        padding 20px
</style>
<style scoped lang="stylus">
    .mental-health-records
        padding 20px
</style>
<style scoped lang="stylus">
    .follow-up-records
        padding 20px
</style>
<style scoped lang="stylus">
    .drawer-header
        height 50px
        line-height 50px
        padding 0 20px
        border-bottom 1px solid #eee
</style>
<style scoped lang="stylus">
    .add-button
        border none
        background none
        color blue
        cursor pointer
    .attachment-container
        border 1px dashed #ccc
        padding 10px
        border-radius 4px
        margin-bottom 10px
    .attachment-list
        display flex
        flex-wrap wrap
        margin-bottom 10px
    .attachment-item
        position relative
        margin-right 10px
        margin-bottom 10px
    .attachment-preview
        width 80px
        height 80px
        border 1px solid #ccc
        border-radius 4px
        overflow hidden
        position relative
    .attachment-preview img
        width 100%
        height 100%
        object-fit contain
    .file-name
        display block
        font-size 12px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        width 80px
        text-align center
        padding 5px 0
    .remove-button
        position absolute
        top 0
        right 0
        width 24px
        height 24px
        background-color rgba(255, 0, 0, 0.7)
        border-radius 50%
        display flex
        align-items center
        justify-content center
        cursor pointer
        z-index 10
    .remove-button i
        color white !important // 设置X的颜色为白色
    .upload-area
        width 80px
        height 80px
        border 1px dashed #ccc
        border-radius 4px
        display flex
        align-items center
        justify-content center
        cursor pointer
        color #888
</style>
<style scoped lang="stylus">
    .attachment-container
        border 1px dashed #ccc
        padding 10px
        border-radius 4px
        margin-bottom 10px
    .attachment-list
        display flex
        flex-wrap wrap
        margin-bottom 10px
    .attachment-item
        position relative
        margin-right 10px
        margin-bottom 10px
    .attachment-preview
        width 80px
        height 80px
        border 1px solid #ccc
        border-radius 4px
        overflow hidden
        position relative
    .attachment-preview img
        width 100%
        height 100%
        object-fit contain
    .file-name
        display block
        font-size 12px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        width 80px
        text-align center
        padding 5px 0
    .remove-button
        position absolute
        top 0
        right 0
        width 24px
        height 24px
        background-color rgba(255, 0, 0, 0.7)
        border-radius 50%
        display flex
        align-items center
        justify-content center
        cursor pointer
        z-index 10
    .remove-button i
        color white !important /* 设置关闭按钮的颜色为白色 */
    .upload-area
        width 80px
        height 80px
        border 1px dashed #ccc
        border-radius 4px
        display flex
        align-items center
        justify-content center
        cursor pointer
        color #888
</style>
<style scoped lang="stylus">
    .attachment-container
        border 1px dashed #ccc
        padding 10px
        border-radius 4px
        margin-bottom 10px
    .attachment-list
        display flex
        flex-wrap wrap
        margin-bottom 10px
    .attachment-item
        position relative
        margin-right 10px
        margin-bottom 10px
    .attachment-preview
        width 80px
        height 80px
        border 1px solid #ccc
        border-radius 4px
        overflow hidden
        position relative
    .attachment-preview img
        width 100%
        height 100%
        object-fit contain
    .file-name
        display block
        font-size 12px
        white-space nowrap
        overflow hidden
        text-overflow ellipsis
        width 80px
        text-align center
        padding 5px 0
    .remove-button
        position absolute
        top 0
        right 0
        width 24px
        height 24px
        background-color rgba(255, 0, 0, 0.7)
        border-radius 50%
        display flex
        align-items center
        justify-content center
        cursor pointer
        z-index 10
    .remove-button i
        color white !important // 设置X的颜色为白色
    .upload-area
        width 80px
        height 80px
        border 1px dashed #ccc
        border-radius 4px
        display flex
        align-items center
        justify-content center
        cursor pointer
        color #888
</style>
<style scoped lang="stylus">
.info-section {
    margin-bottom: 20px;
}

.info-section h3 {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

</style>
